import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Tabs, Table, Card, Statistic, Row, Col, Typography, Button, Collapse, Dropdown, Modal, Breadcrumb, Spin, message } from 'antd';
import { EyeOutlined, ArrowRightOutlined, LeftOutlined, ReloadOutlined } from '@ant-design/icons';
import ReactFlow, { Background, Handle, Position } from 'reactflow';
import 'reactflow/dist/style.css';

const { Title, Text } = Typography;

// SupplySideFlow, SupplySideMetricsTable, DedupeGmvNode, MarketingSideFlow,
// and MarketingSideTable must be defined before MetricsDecompositionModal,
// which uses them.

// 供给向指标流程图组件
const SupplySideFlow = ({ data }) => {
  // 处理数据结构，兼容不同的数据格式
  const getIndicatorData = () => {
    if (!data) return {};

    // 如果数据直接包含指标字段，直接使用
    if (data['GMV'] || data['铺货门店数'] || data['店均在售SKU数']) {
      return data;
    }

    // 如果数据在供给向指标归因字段下
    if (data['供给向指标归因']) {
      return data['供给向指标归因'];
    }

    // 如果数据在indicators_data字段下
    if (data['indicators_data']) {
      return data['indicators_data'];
    }

    // 如果数据在其他嵌套结构中
    return data;
  };

  const indicatorData = getIndicatorData();

  const formatValue = (value, unit = '') => {
    if (value === null || value === undefined || value === '-') return '-';

    // 对于已经是完整字符串的值（如百分比、pp），直接返回
    if (typeof value === 'string' && (value.includes('%') || value.includes('pp'))) {
      return value;
    }

    const num = parseFloat(String(value).replace(/,/g, ''));
    if (isNaN(num)) return value;

    if (unit === '家' || unit === '元') {
      return num.toLocaleString('zh-CN', { maximumFractionDigits: 0 }) + unit;
    }
    if (unit === '个') {
      return num.toLocaleString('zh-CN', { maximumFractionDigits: 3 }) + unit;
    }
    if (unit === '万元') {
       return (num / 10000).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + unit;
    }
    if (unit === '%') {
      // 对于百分比单位，需要将小数转换为百分比
      return (num * 100).toLocaleString('zh-CN', { maximumFractionDigits: 2 }) + unit;
    }
    if (unit === 'pp') {
      // 对于百分点单位，需要将小数转换为百分点，保留两位小数，并添加正负号
      const percentageValue = num * 100;
      return percentageValue >= 0 ? `+${percentageValue.toFixed(2)}${unit}` : `${percentageValue.toFixed(2)}${unit}`;
    }
    return num.toLocaleString('zh-CN', { maximumFractionDigits: 2 }) + unit;
  };

  const getChangeStyle = (value) => {
    if (value === null || value === undefined || value === '-') return {};
    const num = parseFloat(value.toString().replace('%', ''));
    if (isNaN(num)) return {};
    return { color: num >= 0 ? '#3f8600' : '#cf1322' };
  };

  const MultiplierNode = ({ data }) => (
    <div style={{
      width: 40,
      height: 40,
      fontSize: '24px',
      fontWeight: 'bold',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      border: 'none',
      background: 'transparent'
    }}>
      {data.label}
    </div>
  );

  const CustomNode = useCallback(({ data: nodeData }) => (
    <Card
      title={nodeData.label}
      size="small"
      style={{
        width: 280, // 仅加宽卡片
        textAlign: 'center',
        border: '2px solid #1890ff',
        borderRadius: '8px',
      }}
      headStyle={{
        background: '#1890ff',
        color: 'white',
        fontWeight: 'bold',
        borderBottom: 'none',
      }}
      bodyStyle={{ padding: '12px' }}
    >
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gridTemplateRows: 'auto 1fr',
        gap: '8px',
        alignItems: 'stretch',
        minHeight: '80px',
      }}>
        {/* Top Left - 当期值 */}
        <div style={{ gridColumn: '1 / 2', gridRow: '1 / 2', justifySelf: 'start' }}>
          <Statistic
            title="当期值"
            value={formatValue(nodeData.metrics.currentValue, nodeData.metrics.unit)}
            valueStyle={{ fontSize: '1.5em', fontWeight: 'bold' }}
          />
        </div>

        {/* Top Right - 贡献度 */}
        <div style={{ gridColumn: '2 / 3', gridRow: '1 / 2', justifySelf: 'end' }}>
          <Statistic
            title="贡献度"
            value={nodeData.metrics.contribution}
            valueStyle={{ ...getChangeStyle(nodeData.metrics.contribution), fontSize: '1.1em' }}
          />
        </div>

        {/* Bottom Area - 变化值和变化率 */}
        <div style={{ gridColumn: '1 / -1', gridRow: '2 / 3', justifySelf: 'center', alignSelf: 'center' }}>
          <Row gutter={16} justify="center">
            <Col>
              <Statistic
                title="变化值"
                value={formatValue(nodeData.metrics.changeValue, nodeData.metrics.unitChange || nodeData.metrics.unit)}
                valueStyle={{ ...getChangeStyle(nodeData.metrics.changeValue), fontSize: '1.1em' }}
              />
            </Col>
            <Col>
              <Statistic
                title="变化率"
                value={nodeData.metrics.changeRate}
                valueStyle={{ ...getChangeStyle(nodeData.metrics.changeRate), fontSize: '1.1em' }}
              />
            </Col>
          </Row>
        </div>
      </div>
      <Handle type="target" position={Position.Right} />
      <Handle type="source" position={Position.Left} />
    </Card>
  ), []);

  const nodeTypes = useMemo(() => ({ custom: CustomNode, multiplier: MultiplierNode }), [CustomNode]);

  const initialNodes = [
    {
        id: '1',
        type: 'custom',
        data: {
          label: '铺货门店数',
          metrics: {
            unit: '家',
            currentValue: indicatorData['铺货门店数']?.当期值,
            changeValue: indicatorData['铺货门店数']?.变化值,
            changeRate: indicatorData['铺货门店数']?.变化率,
            contribution: indicatorData['铺货门店数']?.贡献度,
          },
        },
        position: { x: 500, y: 0 },
      },
      {
        id: '2',
        type: 'custom',
        data: {
          label: '店均在售SKU数',
          metrics: {
            unit: '个',
            currentValue: indicatorData['店均在售SKU数']?.当期值 || indicatorData['店均在售UPC数']?.当期值,
            changeValue: indicatorData['店均在售SKU数']?.变化值 || indicatorData['店均在售UPC数']?.变化值,
            changeRate: indicatorData['店均在售SKU数']?.变化率 || indicatorData['店均在售UPC数']?.变化率,
            contribution: indicatorData['店均在售SKU数']?.贡献度 || indicatorData['店均在售UPC数']?.贡献度,
          },
        },
        position: { x: 500, y: 250 },
      },
      {
        id: '3',
        type: 'custom',
        data: {
          label: 'SKU平均动销率',
          metrics: {
            unit: '%',
            unitChange: 'pp',
            currentValue: indicatorData['SKU平均动销率']?.当期值,
            changeValue: indicatorData['SKU平均动销率']?.变化值,
            changeRate: indicatorData['SKU平均动销率']?.变化率,
            contribution: indicatorData['SKU平均动销率']?.贡献度,
          },
        },
        position: { x: 500, y: 500 },
      },
      {
        id: '4',
        type: 'custom',
        data: {
          label: '动销SKU平均GMV',
          metrics: {
            unit: '元',
            currentValue: indicatorData['动销SKU平均GMV']?.当期值,
            changeValue: indicatorData['动销SKU平均GMV']?.变化值,
            changeRate: indicatorData['动销SKU平均GMV']?.变化率,
            contribution: indicatorData['动销SKU平均GMV']?.贡献度,
          },
        },
        position: { x: 500, y: 750 },
      },
      {
        id: 'x1',
        type: 'multiplier',
        data: { label: '×' },
        position: { x: 620, y: 195 },
        draggable: false,
      },
      {
        id: 'x2',
        type: 'multiplier',
        data: { label: '×' },
        position: { x: 620, y: 445 },
        draggable: false,
      },
      {
        id: 'x3',
        type: 'multiplier',
        data: { label: '×' },
        position: { x: 620, y: 695 },
        draggable: false,
      },
      {
        id: '5',
        type: 'custom', // 改为custom类型以统一卡片样式
        data: {
          label: 'GMV',
          metrics: {
            unit: '万元',
            currentValue: indicatorData.GMV?.当期值,
            changeValue: indicatorData.GMV?.变化值,
            changeRate: indicatorData.GMV?.变化率,
            contribution: indicatorData.GMV?.贡献度,
          },
        },
        position: { x: 50, y: 300 },
      },
  ];

  const initialEdges = [
    { id: 'e1-5', source: '1', target: '5', animated: true, style: { strokeWidth: 2 } },
    { id: 'e2-5', source: '2', target: '5', animated: true, style: { strokeWidth: 2 } },
    { id: 'e3-5', source: '3', target: '5', animated: true, style: { strokeWidth: 2 } },
    { id: 'e4-5', source: '4', target: '5', animated: true, style: { strokeWidth: 2 } },
  ];

  return (
    <div style={{
      height: 550,
      border: '1px solid #eee',
      borderRadius: '8px',
      position: 'relative',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{ margin: '16px', display: 'flex', alignItems: 'center', gap: '8px' }}>
        <h4 style={{ margin: 0, fontWeight: 'bold' }}>供给向指标归因分析</h4>
        <span style={{
          fontSize: '10px',
          color: '#999',
          backgroundColor: '#f5f5f5',
          padding: '2px 6px',
          borderRadius: '4px',
          fontWeight: 'normal',
          whiteSpace: 'nowrap'
        }}>
          目前展示仅美团供给数据，其余平台供给数据筹备中，敬请期待
        </span>
      </div>
      <div style={{
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative'
      }}>
        <ReactFlow
          nodes={initialNodes}
          edges={initialEdges}
          nodeTypes={nodeTypes}
          fitView
          style={{ width: '100%', height: '100%' }}
        >
          <Background variant="dots" gap={12} size={1} />
        </ReactFlow>
      </div>
    </div>
  );
};

// 供给向指标表格组件
const SupplySideMetricsTable = ({ data }) => {
  // 处理数据结构，兼容不同的数据格式
  const getIndicatorData = () => {
    if (!data) return {};

    // 如果数据直接包含指标字段，直接使用
    if (data['GMV'] || data['铺货门店数']) {
      return data;
    }

    // 如果数据在供给向指标归因字段下
    if (data['供给向指标归因']) {
      return data['供给向指标归因'];
    }

    // 如果数据在其他嵌套结构中
    return data;
  };

  const indicatorData = getIndicatorData();



  // 根据变化率获取行颜色 - 绿涨红跌，0为黑色
  const getRowColorByChangeRate = (changeRate) => {
    if (!changeRate || changeRate === '-') return '#333';
    const numRate = parseFloat(changeRate.toString().replace(/[%,+pp]/g, ''));
    if (isNaN(numRate)) return '#333';
    if (numRate === 0) return '#333'; // 变化率为0时显示黑色
    return numRate > 0 ? '#52c41a' : '#f5222d'; // 绿涨红跌
  };

  // 格式化SKU平均动销率数据
  const formatSKUTurnoverRate = (value, type) => {
    if (!value || value === '0' || value === '-') return '-';

    // 处理字符串，移除百分号、逗号、加号等符号
    const cleanValue = value.toString().replace(/[%,+pp]/g, '');
    const numValue = parseFloat(cleanValue);
    if (isNaN(numValue)) return value;

    if (type === 'changeValue') {
      // 变化值使用pp格式，需要乘以100转换为百分点
      const percentageValue = numValue * 100;
      return percentageValue >= 0 ? `+${percentageValue.toFixed(2)}pp` : `${percentageValue.toFixed(2)}pp`;
    } else {
      // 其他值使用百分数格式，需要乘以100转换为百分比，保留两位小数
      const percentageValue = numValue * 100;
      return `${percentageValue.toFixed(2)}%`;
    }
  };

  // 表格数据
  const tableData = [
    {
      indicator: 'GMV',
      currentValue: indicatorData.GMV?.当期值 || '0',
      baseValue: indicatorData.GMV?.对比期值 || '0',
      changeValue: indicatorData.GMV?.变化值 || '0',
      changeRate: indicatorData.GMV?.变化率 || '0.00%',
      contribution: indicatorData.GMV?.贡献度 || '0.00%'
    },
    {
      indicator: '铺货门店数',
      currentValue: indicatorData.铺货门店数?.当期值 || '0',
      baseValue: indicatorData.铺货门店数?.对比期值 || '0',
      changeValue: indicatorData.铺货门店数?.变化值 || '0',
      changeRate: indicatorData.铺货门店数?.变化率 || '0.00%',
      contribution: indicatorData.铺货门店数?.贡献度 || '0.00%'
    },
    {
      indicator: '店均在售SKU数',
      currentValue: indicatorData.店均在售SKU数?.当期值 || indicatorData.店均在售UPC数?.当期值 || '0',
      baseValue: indicatorData.店均在售SKU数?.对比期值 || indicatorData.店均在售UPC数?.对比期值 || '0',
      changeValue: indicatorData.店均在售SKU数?.变化值 || indicatorData.店均在售UPC数?.变化值 || '0',
      changeRate: indicatorData.店均在售SKU数?.变化率 || indicatorData.店均在售UPC数?.变化率 || '0.00%',
      contribution: indicatorData.店均在售SKU数?.贡献度 || indicatorData.店均在售UPC数?.贡献度 || '0.00%'
    },
    {
      indicator: 'SKU平均动销率',
      currentValue: formatSKUTurnoverRate(indicatorData.SKU平均动销率?.当期值, 'currentValue'),
      baseValue: formatSKUTurnoverRate(indicatorData.SKU平均动销率?.对比期值, 'baseValue'),
      changeValue: formatSKUTurnoverRate(indicatorData.SKU平均动销率?.变化值, 'changeValue'),
      changeRate: formatSKUTurnoverRate(indicatorData.SKU平均动销率?.变化率, 'changeRate'),
      contribution: formatSKUTurnoverRate(indicatorData.SKU平均动销率?.贡献度, 'contribution')
    },
    {
      indicator: '动销SKU平均GMV',
      currentValue: indicatorData.动销SKU平均GMV?.当期值 || '0',
      baseValue: indicatorData.动销SKU平均GMV?.对比期值 || '0',
      changeValue: indicatorData.动销SKU平均GMV?.变化值 || '0',
      changeRate: indicatorData.动销SKU平均GMV?.变化率 || '0.00%',
      contribution: indicatorData.动销SKU平均GMV?.贡献度 || '0.00%'
    }
  ];

  const columns = [
    {
      title: '指标名称',
      dataIndex: 'indicator',
      key: 'indicator',
      width: 150,
      className: 'supply-side-indicator-column',
      render: (text) => <span style={{ color: '#333' }}>{text}</span> // 指标名称保持黑色
    },
    {
      title: '当期值',
      dataIndex: 'currentValue',
      key: 'currentValue',
      width: 120,
      className: 'supply-side-number-column',
      render: (text, record) => <span style={{ color: getRowColorByChangeRate(record.changeRate), fontWeight: 'bold' }}>{text}</span>
    },
    {
      title: '对比期值',
      dataIndex: 'baseValue',
      key: 'baseValue',
      width: 120,
      className: 'supply-side-number-column',
      render: (text, record) => <span style={{ color: getRowColorByChangeRate(record.changeRate), fontWeight: 'bold' }}>{text}</span>
    },
    {
      title: '变化值',
      dataIndex: 'changeValue',
      key: 'changeValue',
      width: 120,
      className: 'supply-side-change-column',
      render: (text, record) => <span style={{ color: getRowColorByChangeRate(record.changeRate), fontWeight: 'bold' }}>{text}</span>
    },
    {
      title: '变化率',
      dataIndex: 'changeRate',
      key: 'changeRate',
      width: 100,
      className: 'supply-side-rate-column',
      render: (text, record) => <span style={{ color: getRowColorByChangeRate(record.changeRate), fontWeight: 'bold' }}>{text}</span>
    },
    {
      title: '贡献度',
      dataIndex: 'contribution',
      key: 'contribution',
      width: 100,
      className: 'supply-side-contribution-column',
      render: (text, record) => <span style={{ color: getRowColorByChangeRate(record.changeRate), fontWeight: 'bold' }}>{text}</span>
    }
  ];

  return (
    <div className="supply-side-table-container" style={{ marginTop: '40px' }}>
      <h4 style={{ margin: '16px', fontSize: '16px', fontWeight: 'bold' }}>详细数据</h4>
      <div style={{
        overflowX: 'auto',
        overflowY: 'hidden',
        border: '1px solid #f0f0f0',
        borderRadius: '6px',
        background: '#fff'
      }}>
        <Table
          columns={columns}
          dataSource={tableData}
          pagination={false}
          size="small"
          bordered
          rowKey="indicator"
          className="supply-side-metrics-table"
          style={{
            background: '#fff',
            minWidth: '800px' // 设置最小宽度确保表格不会过度压缩
          }}
          scroll={false} // 禁用Table自身的滚动，让外层容器处理
          onRow={() => ({
            style: {} // 不在行级别设置颜色，改为在列级别设置
          })}
        />
      </div>
      <style jsx>{`
        .supply-side-table-container .ant-table-thead > tr > th {
          font-weight: bold;
          text-align: center;
        }
        .supply-side-table-container .ant-table-tbody > tr > td {
          text-align: center;
          vertical-align: middle;
        }
        .supply-side-table-container .supply-side-indicator-column {
          font-weight: bold;
        }
      `}</style>
    </div>
  );
};

// 去重活动GMV汇总节点（仅展示汇总，不再下钻）
const DedupeGmvNode = ({ data }) => {
  const { indicators } = data;
  if (!indicators || !indicators['去重活动GMV']) {
    return null;
  }

  const dedupeData = indicators['去重活动GMV'];

  const formatValue = (value, unit = '') => {
    if (value === null || value === undefined || value === '-') return '-';
    if (typeof value === 'string' && (value.includes('%') || value.includes('pp'))) return value;
    const num = parseFloat(String(value).replace(/,/g, ''));
    if (isNaN(num)) return value;
    if (unit === '万元') {
      return (num / 10000).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + unit;
    }
    return num.toLocaleString('zh-CN', { maximumFractionDigits: 2 }) + unit;
  };

  const getChangeStyle = (value) => {
    if (value === null || value === undefined || value === '-') return {};
    const num = parseFloat(String(value).replace('%', ''));
    if (isNaN(num)) return {};
    return { color: num >= 0 ? '#3f8600' : '#cf1322' };
  };

  const DedupeGmvHeader = () => (
    <Card
      title="去重活动GMV"
      size="small"
      style={{
        width: 280, // 与自然GMV卡片保持一致的宽度
        textAlign: 'center',
        border: '2px solid #1890ff',
        borderRadius: '8px'
      }}
      headStyle={{
        background: '#1890ff',
        color: 'white',
        fontWeight: 'bold',
        borderBottom: 'none' // 与自然GMV卡片保持一致
      }}
      bodyStyle={{ padding: '12px' }}
    >
      {/* 使用与自然GMV卡片相同的网格布局格式 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gridTemplateRows: 'auto 1fr',
        gap: '8px',
        alignItems: 'stretch',
        minHeight: '80px',
      }}>
        {/* Top Left - 当期值 */}
        <div style={{ gridColumn: '1 / 2', gridRow: '1 / 2', justifySelf: 'start' }}>
          <Statistic
            title="当期值"
            value={formatValue(dedupeData.当期值, '万元')}
            valueStyle={{ fontSize: '1.5em', fontWeight: 'bold' }}
          />
        </div>
        {/* Top Right - 贡献度 */}
        <div style={{ gridColumn: '2 / 3', gridRow: '1 / 2', justifySelf: 'end' }}>
          <Statistic
            title="贡献度"
            value={dedupeData.贡献度}
            valueStyle={{ ...getChangeStyle(dedupeData.贡献度), fontSize: '1.1em' }}
          />
        </div>
        {/* Bottom - 变化值和变化率 */}
        <div style={{ gridColumn: '1 / -1', gridRow: '2 / 3', justifySelf: 'center', alignSelf: 'center' }}>
          <Row gutter={16} justify="center">
            <Col>
              <Statistic
                title="变化值"
                value={formatValue(dedupeData.变化值, '万元')}
                valueStyle={{ ...getChangeStyle(dedupeData.变化值), fontSize: '1.1em' }}
              />
            </Col>
            <Col>
              <Statistic
                title="变化率"
                value={dedupeData.变化率}
                valueStyle={{ ...getChangeStyle(dedupeData.变化率), fontSize: '1.1em' }}
              />
            </Col>
          </Row>
        </div>
      </div>
    </Card>
  );

  // 仅展示汇总，不再展开活动机制
  return <div style={{ width: 280 }}><DedupeGmvHeader /></div>;
};

// 营销向指标流程图组件
const MarketingSideFlow = ({ data }) => {
  // 状态管理：控制下钻层级和展开状态
  const [showDrillDownOptions, setShowDrillDownOptions] = useState(false); // 是否显示下钻选项卡片
  const [selectedDrillDownType, setSelectedDrillDownType] = useState(null); // 'indicators' | 'mechanisms' | null
  const [selectedMechanismType, setSelectedMechanismType] = useState(null); // 选中的机制类型
  const [expandedMechanismDetails, setExpandedMechanismDetails] = useState({});

  // --- 复用供给向的辅助函数和节点组件 ---
  const formatValue = (value, unit = '') => {
    if (value === null || value === undefined || value === '-') return '-';
    if (typeof value === 'string' && (value.includes('%') || value.includes('pp'))) return value;
    const num = parseFloat(String(value).replace(/,/g, ''));
    if (isNaN(num)) return value;
    if (unit === '万元') {
       return (num / 10000).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + unit;
    }
    return num.toLocaleString('zh-CN', { maximumFractionDigits: 2 }) + unit;
  };

  const getChangeStyle = useCallback((value) => {
    if (value === null || value === undefined || value === '-') return {};
    const num = parseFloat(String(value).replace('%', ''));
    if (isNaN(num)) return {};
    return { color: num >= 0 ? '#3f8600' : '#cf1322' };
  }, []);

  // 可点击的活动GMV节点 - 右上角显示"点击下钻"
  const ClickableActivityGmvNode = useCallback(({ data: nodeData }) => {
    const handleDrillDownClick = () => {
      const newShowOptions = !showDrillDownOptions;
      setShowDrillDownOptions(newShowOptions);

      // 如果关闭下钻选项，重置所有相关状态
      if (!newShowOptions) {
        setSelectedDrillDownType(null);
        setSelectedMechanismType(null);
        setExpandedMechanismDetails({});
      }
    };

    return (
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{nodeData.label}</span>
            <span
              onClick={handleDrillDownClick}
              style={{
                fontSize: '10px',
                cursor: 'pointer',
                textDecoration: 'underline',
                opacity: 0.8
              }}
            >
              点击下钻
            </span>
          </div>
        }
        size="small"
        style={{
          width: 280,
          textAlign: 'center',
          border: '2px solid #1890ff',
          borderRadius: '8px',
          transition: 'all 0.3s ease',
          position: 'relative'
        }}
        headStyle={{ background: '#1890ff', color: 'white', fontWeight: 'bold', borderBottom: 'none' }}
        bodyStyle={{ padding: '12px' }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'scale(1.02)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.3)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = 'none';
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Statistic title="当期值" value={formatValue(nodeData.metrics.currentValue, nodeData.metrics.unit)} valueStyle={{ fontSize: '1.5em', fontWeight: 'bold' }} />
            <Statistic title="贡献度" value={nodeData.metrics.contribution} valueStyle={{ ...getChangeStyle(nodeData.metrics.contribution), fontSize: '1.1em' }} />
          </div>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: '32px' }}>
            <Statistic title="变化值" value={formatValue(nodeData.metrics.changeValue, nodeData.metrics.unitChange || nodeData.metrics.unit)} valueStyle={{ ...getChangeStyle(nodeData.metrics.changeValue), fontSize: '1.1em' }} />
            <Statistic title="变化率" value={nodeData.metrics.changeRate} valueStyle={{ ...getChangeStyle(nodeData.metrics.changeRate), fontSize: '1.1em' }} />
          </div>


        </div>
        <Handle type="target" position={Position.Right} />
        <Handle type="source" position={Position.Left} />
        <Handle type="source" position={Position.Right} id="drill-down-source" style={{ top: '50%', right: '-8px' }} />
      </Card>
    );
  }, [showDrillDownOptions, getChangeStyle]);

  // 下钻指标选项卡片 - 单选框类型
  const DrillDownIndicatorsCard = useCallback(({ data: _ }) => {
    const isSelected = selectedDrillDownType === 'indicators';

    const handleSelect = () => {
      if (isSelected) {
        setSelectedDrillDownType(null);
      } else {
        setSelectedDrillDownType('indicators');
        // 切换到指标时，重置机制类型选择
        setSelectedMechanismType(null);
      }
    };

    return (
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>下钻指标</span>
            <input
              type="radio"
              checked={isSelected}
              onChange={handleSelect}
              style={{ cursor: 'pointer' }}
            />
          </div>
        }
        size="small"
        style={{
          width: 200,
          textAlign: 'center',
          border: `2px solid ${isSelected ? '#1890ff' : '#d9d9d9'}`,
          borderRadius: '8px',
          transition: 'all 0.3s ease',
          backgroundColor: isSelected ? '#f0f8ff' : 'white',
          cursor: 'pointer'
        }}
        headStyle={{
          background: isSelected ? '#1890ff' : '#f5f5f5',
          color: isSelected ? 'white' : '#666',
          fontWeight: 'bold',
          borderBottom: 'none'
        }}
        bodyStyle={{ padding: '12px' }}
        onClick={handleSelect}
        onMouseEnter={(e) => {
          if (!isSelected) {
            e.currentTarget.style.transform = 'scale(1.02)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.2)';
          }
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = 'none';
        }}
      >
        <div style={{ fontSize: '14px', color: isSelected ? '#1890ff' : '#666' }}>
          查看指标分解
        </div>
        <Handle type="target" position={Position.Left} />
        {isSelected && <Handle type="source" position={Position.Right} id="indicators-drill-source" style={{ top: '50%', right: '-8px' }} />}
      </Card>
    );
  }, [selectedDrillDownType]);

  // 下钻机制选项卡片 - 单选框类型
  const DrillDownMechanismsCard = useCallback(({ data: _ }) => {
    const isSelected = selectedDrillDownType === 'mechanisms';

    const handleSelect = () => {
      if (isSelected) {
        setSelectedDrillDownType(null);
        // 取消选择机制时，重置机制类型选择
        setSelectedMechanismType(null);
      } else {
        setSelectedDrillDownType('mechanisms');
      }
    };

    return (
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>下钻机制</span>
            <input
              type="radio"
              checked={isSelected}
              onChange={handleSelect}
              style={{ cursor: 'pointer' }}
            />
          </div>
        }
        size="small"
        style={{
          width: 200,
          textAlign: 'center',
          border: `2px solid ${isSelected ? '#1890ff' : '#d9d9d9'}`,
          borderRadius: '8px',
          transition: 'all 0.3s ease',
          backgroundColor: isSelected ? '#f0f8ff' : 'white',
          cursor: 'pointer'
        }}
        headStyle={{
          background: isSelected ? '#1890ff' : '#f5f5f5',
          color: isSelected ? 'white' : '#666',
          fontWeight: 'bold',
          borderBottom: 'none'
        }}
        bodyStyle={{ padding: '12px' }}
        onClick={handleSelect}
        onMouseEnter={(e) => {
          if (!isSelected) {
            e.currentTarget.style.transform = 'scale(1.02)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.2)';
          }
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = 'none';
        }}
      >
        <div style={{ fontSize: '14px', color: isSelected ? '#1890ff' : '#666' }}>
          查看机制详情
        </div>
        <Handle type="target" position={Position.Left} />
        {isSelected && <Handle type="source" position={Position.Right} id="mechanisms-drill-source" style={{ top: '50%', right: '-8px' }} />}
      </Card>
    );
  }, [selectedDrillDownType]);

  // 机制类型卡片组件 - 类似活动GMV的样式
  const MechanismTypeCard = useCallback(({ data: nodeData }) => {
    const mechanismType = nodeData.mechanismType;
    const isSelected = selectedMechanismType === mechanismType;

    const handleDrillDown = () => {
      if (isSelected) {
        setSelectedMechanismType(null); // 如果已选中，则取消选择
      } else {
        setSelectedMechanismType(mechanismType); // 否则选择该机制类型
      }
    };

    // 格式化数值显示
    const formatMechanismValue = (value, unit = '万元') => {
      if (!value) return '0';
      const numValue = typeof value === 'string' ? parseFloat(value.replace(/,/g, '')) : value;
      return `${(numValue / 10000).toFixed(2)}${unit}`;
    };

    const currentValue = nodeData.data?.当期值 || nodeData.data?.['当期GMV'] || '0';
    const changeValue = nodeData.data?.变化值 || nodeData.data?.['GMV变化值'] || '0';
    const changeRate = nodeData.data?.变化率 || nodeData.data?.['变化率'] || '0.00%';
    const contribution = nodeData.data?.贡献度 || '0.00%';

    return (
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{mechanismType}</span>
            <span
              onClick={handleDrillDown}
              style={{
                fontSize: '10px',
                cursor: 'pointer',
                textDecoration: 'underline',
                opacity: 0.8
              }}
            >
              {isSelected ? '取消下钻' : '点击下钻'}
            </span>
          </div>
        }
        size="small"
        style={{
          width: 280,
          textAlign: 'center',
          border: `2px solid ${isSelected ? '#52c41a' : '#1890ff'}`, // 选中时显示绿色边框
          borderRadius: '8px',
          transition: 'all 0.3s ease',
          position: 'relative',
          backgroundColor: isSelected ? '#f6ffed' : 'white' // 选中时显示浅绿色背景
        }}
        headStyle={{
          background: isSelected ? '#52c41a' : '#1890ff', // 选中时显示绿色标题栏
          color: 'white',
          fontWeight: 'bold',
          borderBottom: 'none'
        }}
        bodyStyle={{ padding: '12px' }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'scale(1.02)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.3)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = 'none';
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Statistic
              title="当期值"
              value={formatMechanismValue(currentValue)}
              valueStyle={{ fontSize: '1.5em', fontWeight: 'bold' }}
            />
            <Statistic
              title="贡献度"
              value={contribution}
              valueStyle={{ ...getChangeStyle(contribution), fontSize: '1.1em' }}
            />
          </div>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: '32px' }}>
            <Statistic
              title="变化值"
              value={formatMechanismValue(changeValue)}
              valueStyle={{ ...getChangeStyle(changeValue), fontSize: '1.1em' }}
            />
            <Statistic
              title="变化率"
              value={changeRate}
              valueStyle={{ ...getChangeStyle(changeRate), fontSize: '1.1em' }}
            />
          </div>
        </div>
        <Handle type="target" position={Position.Left} />
        <Handle type="source" position={Position.Right} id="mechanism-type-source" style={{ top: '50%', right: '-8px' }} />
      </Card>
    );
  }, [selectedMechanismType, setSelectedMechanismType, getChangeStyle]);

  // 机制节点组件
  const MechanismNode = useCallback(({ data: nodeData }) => {
    const handleClick = () => {
      setExpandedMechanismDetails(prev => ({
        ...prev,
        [nodeData.mechanismName]: !prev[nodeData.mechanismName]
      }));
    };

    // 格式化机制数值显示
    const formatMechanismValue = (value, unit = '万元') => {
      if (!value || value === '-') return '0';
      const numValue = typeof value === 'string' ? parseFloat(value.replace(/,/g, '')) : value;
      if (isNaN(numValue)) return '0';
      return `${(numValue / 10000).toFixed(2)}${unit}`;
    };

    return (
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{nodeData.label}</span>
            <span
              onClick={(e) => {
                e.stopPropagation();
                handleClick();
              }}
              style={{
                fontSize: '10px',
                cursor: 'pointer',
                textDecoration: 'underline',
                opacity: 0.8
              }}
            >
              点击下钻
            </span>
          </div>
        }
        size="small"
        style={{
          width: 280, // 与主要指标卡片保持一致的宽度
          textAlign: 'center',
          border: '2px solid #1890ff',
          borderRadius: '8px',
          transition: 'all 0.3s ease'
        }}
        headStyle={{ background: '#1890ff', color: 'white', fontWeight: 'bold', borderBottom: 'none' }}
        bodyStyle={{ padding: '12px' }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'scale(1.02)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.3)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = 'none';
        }}
      >
        {/* 使用与主要指标卡片相同的布局格式 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Statistic
              title="当期值"
              value={formatMechanismValue(nodeData.data.当期GMV || nodeData.data.当期值 || '0')}
              valueStyle={{ fontSize: '1.5em', fontWeight: 'bold' }}
            />
            <Statistic
              title="贡献度"
              value={nodeData.data.贡献度 || '0.00%'}
              valueStyle={{ ...getChangeStyle(nodeData.data.贡献度), fontSize: '1.1em' }}
            />
          </div>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: '32px' }}>
            <Statistic
              title="变化值"
              value={formatMechanismValue(nodeData.data.GMV变化值 || nodeData.data.变化值 || '0')}
              valueStyle={{ ...getChangeStyle(nodeData.data.GMV变化值 || nodeData.data.变化值), fontSize: '1.1em' }}
            />
            <Statistic
              title="变化率"
              value={nodeData.data.变化率 || '0.00%'}
              valueStyle={{ ...getChangeStyle(nodeData.data.变化率), fontSize: '1.1em' }}
            />
          </div>
        </div>
        <Handle type="target" position={Position.Left} />
        <Handle type="source" position={Position.Right} />
      </Card>
    );
  }, [setExpandedMechanismDetails, getChangeStyle]);

  // 机制详情卡片组件（核销金额）- 采用去重活动GMV的单行布局格式
  const MechanismSubsidyCard = useCallback(({ data: nodeData }) => {
    return (
      <Card
        title={`${nodeData.mechanismName} - 核销金额`}
        size="small"
        style={{
          width: 420, // 增加宽度以适应单行布局
          textAlign: 'center',
          border: '2px solid #1890ff',
          borderRadius: '8px'
        }}
        headStyle={{ background: '#1890ff', color: 'white', fontWeight: 'bold', borderBottom: 'none' }}
        bodyStyle={{ padding: '12px' }}
      >
        {/* 采用去重活动GMV的单行布局格式 */}
        <Row gutter={16}>
          <Col span={6}><Statistic title="当期值" value={formatValue(nodeData.data.当期核销金额, '万元')} valueStyle={{ fontSize: '1.2em' }} /></Col>
          <Col span={6}><Statistic title="变化值" value={formatValue(nodeData.data.核销金额变化值, '万元')} valueStyle={{ ...getChangeStyle(nodeData.data.核销金额变化值), fontSize: '1.2em' }} /></Col>
          <Col span={6}><Statistic title="变化率" value={nodeData.data.核销金额变化率} valueStyle={{ ...getChangeStyle(nodeData.data.核销金额变化率), fontSize: '1.2em' }} /></Col>
          <Col span={6}><Statistic title="贡献度" value={nodeData.data.核销金额贡献度} valueStyle={{ ...getChangeStyle(nodeData.data.核销金额贡献度), fontSize: '1.2em' }} /></Col>
        </Row>
        <Handle type="target" position={Position.Left} />
      </Card>
    );
  }, [getChangeStyle]);

  // 机制详情卡片组件（ROI）- 采用去重活动GMV的单行布局格式
  const MechanismRoiCard = useCallback(({ data: nodeData }) => {
    return (
      <Card
        title={`${nodeData.mechanismName} - ROI`}
        size="small"
        style={{
          width: 420, // 增加宽度以适应单行布局
          textAlign: 'center',
          border: '2px solid #1890ff',
          borderRadius: '8px'
        }}
        headStyle={{ background: '#1890ff', color: 'white', fontWeight: 'bold', borderBottom: 'none' }}
        bodyStyle={{ padding: '12px' }}
      >
        {/* 采用去重活动GMV的单行布局格式 */}
        <Row gutter={16}>
          <Col span={6}><Statistic title="当期值" value={nodeData.data.当期ROI} valueStyle={{ fontSize: '1.2em' }} /></Col>
          <Col span={6}><Statistic title="变化值" value={nodeData.data.ROI变化值} valueStyle={{ ...getChangeStyle(nodeData.data.ROI变化值), fontSize: '1.2em' }} /></Col>
          <Col span={6}><Statistic title="变化率" value={nodeData.data.ROI变化率} valueStyle={{ ...getChangeStyle(nodeData.data.ROI变化率), fontSize: '1.2em' }} /></Col>
          <Col span={6}><Statistic title="贡献度" value={nodeData.data.ROI贡献度} valueStyle={{ ...getChangeStyle(nodeData.data.ROI贡献度), fontSize: '1.2em' }} /></Col>
        </Row>
        <Handle type="target" position={Position.Left} />
      </Card>
    );
  }, [getChangeStyle]);

  // "+"号节点组件 - 类似自然GMV和活动GMV中间的样式
  const PlusSignNode = () => {
    return (
      <div
        style={{
          width: '40px',
          height: '40px',
          fontSize: '24px',
          fontWeight: 'bold',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'transparent'
        }}
      >
        +
      </div>
    );
  };

  const CustomNode = useCallback(({ data: nodeData }) => (
    <Card
      title={nodeData.label}
      size="small"
      style={{ width: 280, textAlign: 'center', border: '2px solid #1890ff', borderRadius: '8px' }}
      headStyle={{ background: '#1890ff', color: 'white', fontWeight: 'bold', borderBottom: 'none' }}
      bodyStyle={{ padding: '12px' }}
    >
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gridTemplateRows: 'auto 1fr', gap: '8px', alignItems: 'stretch', minHeight: '80px' }}>
        <div style={{ gridColumn: '1 / 2', gridRow: '1 / 2', justifySelf: 'start' }}>
          <Statistic title="当期值" value={formatValue(nodeData.metrics.currentValue, nodeData.metrics.unit)} valueStyle={{ fontSize: '1.5em', fontWeight: 'bold' }} />
        </div>
        <div style={{ gridColumn: '2 / 3', gridRow: '1 / 2', justifySelf: 'end' }}>
          <Statistic title="贡献度" value={nodeData.metrics.contribution} valueStyle={{ ...getChangeStyle(nodeData.metrics.contribution), fontSize: '1.1em' }} />
        </div>
        <div style={{ gridColumn: '1 / -1', gridRow: '2 / 3', justifySelf: 'center', alignSelf: 'center' }}>
          <Row gutter={16} justify="center">
            <Col><Statistic title="变化值" value={formatValue(nodeData.metrics.changeValue, nodeData.metrics.unitChange || nodeData.metrics.unit)} valueStyle={{ ...getChangeStyle(nodeData.metrics.changeValue), fontSize: '1.1em' }} /></Col>
            <Col><Statistic title="变化率" value={nodeData.metrics.changeRate} valueStyle={{ ...getChangeStyle(nodeData.metrics.changeRate), fontSize: '1.1em' }} /></Col>
          </Row>
        </div>
      </div>
      <Handle type="target" position={Position.Right} />
      <Handle type="target" position={Position.Left} id="left" />
      <Handle type="source" position={Position.Left} />
    </Card>
  ), [getChangeStyle]);

  const OperatorNode = ({ data }) => (
    <div style={{ width: 40, height: 40, fontSize: '24px', fontWeight: 'bold', display: 'flex', alignItems: 'center', justifyContent: 'center', background: 'transparent' }}>
      {data.label}
    </div>
  );

  // 获取活动机制数据
  const getMechanismData = () => {
    const indicators = data?.indicators_data || {};
    const activityGmv = indicators['活动GMV'];
    if (!activityGmv || !activityGmv.活动机制) {
      return [];
    }

    const mechanismData = activityGmv.活动机制;
    return Object.keys(mechanismData).filter(name => !name.startsWith('_')).map(name => ({
      name,
      data: mechanismData[name]
    }));
  };

  // 获取机制类型数据
  const getMechanismTypesData = () => {
    const indicators = data?.indicators_data || {};
    const activityGmv = indicators['活动GMV'];
    if (!activityGmv || !activityGmv.活动机制 || !activityGmv.活动机制._mechanism_types) {
      return [];
    }

    const mechanismTypesData = activityGmv.活动机制._mechanism_types;
    if (!mechanismTypesData || Object.keys(mechanismTypesData).length === 0) {
      return [];
    }

    // 直接使用后端计算好的机制类型汇总数据
    return Object.keys(mechanismTypesData).map(typeName => {
      const typeData = mechanismTypesData[typeName];

      return {
        typeName,
        data: {
          当期值: typeData.当期值 || '0',
          变化值: typeData.变化值 || '0',
          变化率: typeData.变化率 || '0.00%',
          贡献度: typeData.贡献度 || '0.00%',
          mechanisms: typeData.mechanisms || []
        }
      };
    });
  };



  const nodeTypes = useMemo(() => ({
    custom: CustomNode,
    clickableActivity: ClickableActivityGmvNode,
    drillDownIndicators: DrillDownIndicatorsCard,
    drillDownMechanisms: DrillDownMechanismsCard,
    mechanismType: MechanismTypeCard,
    mechanism: MechanismNode,
    mechanismSubsidy: MechanismSubsidyCard,
    mechanismRoi: MechanismRoiCard,
    operator: OperatorNode,
    dedupe: DedupeGmvNode,
    plusSign: PlusSignNode
  }), [ClickableActivityGmvNode, DrillDownIndicatorsCard, DrillDownMechanismsCard, MechanismTypeCard, MechanismNode, MechanismSubsidyCard, MechanismRoiCard, CustomNode]);
  
  // 确保有数据
  if (!data || !data.indicators_data || Object.keys(data.indicators_data).length === 0) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: '#999' }}>
        暂无营销向指标数据
      </div>
    );
  }
  const indicators = data.indicators_data;

  // --- 定义节点和边 ---
  const initialNodes = [
    // Column 1: GMV
    {
      id: 'gmv', type: 'custom', position: { x: 0, y: 210 },
      data: {
        label: 'GMV',
        metrics: {
          unit: '万元',
          currentValue: indicators['总GMV']?.当期值,
          changeValue: indicators['总GMV']?.变化值,
          changeRate: indicators['总GMV']?.变化率,
          contribution: indicators['总GMV']?.贡献度,
        }
      }
    },
    // Column 2: 自然GMV + 活动GMV (交换位置，增加列间距)
    {
      id: 'natural_gmv', type: 'custom', position: { x: 420, y: 60 },
      data: {
        label: '自然GMV',
        metrics: {
          unit: '万元',
          currentValue: indicators['自然GMV']?.当期值,
          changeValue: indicators['自然GMV']?.变化值,
          changeRate: indicators['自然GMV']?.变化率,
          contribution: indicators['自然GMV']?.贡献度,
        }
      }
    },
    {
      id: 'activity_gmv', type: 'clickableActivity', position: { x: 420, y: 380 },
      data: {
        label: '活动GMV',
        metrics: {
          unit: '万元',
          currentValue: indicators['活动GMV']?.当期值,
          changeValue: indicators['活动GMV']?.变化值,
          changeRate: indicators['活动GMV']?.变化率,
          contribution: indicators['活动GMV']?.贡献度,
        }
      }
    },
    {
      id: 'plus', type: 'operator', position: { x: 540, y: 300 }, // 自然GMV(y:60) + 活动GMV(y:380) 的中心位置
      data: { label: '+' }, draggable: false,
    },
    // 去重活动GMV放在活动GMV下方对齐，与活动GMV在同一个垂直面上
    {
      id: 'dedupe_gmv',
      type: 'dedupe',
      position: { x: 420, y: 650 },
      data: { indicators },
      draggable: false,
    },
    // 下钻选项卡片：在活动GMV右侧对称位置
    ...(showDrillDownOptions ? [
      // 下钻指标卡片（活动GMV右上方）- 只在未选择下钻机制时显示
      ...(selectedDrillDownType !== 'mechanisms' ? [{
        id: 'drill_indicators',
        type: 'drillDownIndicators',
        position: { x: 800, y: selectedDrillDownType === 'indicators' ? 427 : 300 }, // 选中时与活动GMV水平对齐(y=380)
        data: {},
        draggable: false,
      }] : []),
      // 下钻机制卡片（活动GMV右下方）- 只在未选择下钻指标时显示
      ...(selectedDrillDownType !== 'indicators' ? [{
        id: 'drill_mechanisms',
        type: 'drillDownMechanisms',
        position: { x: 800, y: selectedDrillDownType === 'mechanisms' ? 427 : 550 }, // 选中时与活动GMV水平对齐(y=380)
        data: {},
        draggable: false,
      }] : [])
    ] : []),

    // 指标分解内容：以下钻指标卡片为中心对称分布
    ...(selectedDrillDownType === 'indicators' ? [
      // 活动机制核销金额（位于下钻指标卡片上方）
      {
        id: 'subsidy',
        type: 'custom',
        position: { x: 1200, y: 250 }, // 下钻指标y:427 - 177 = 250
        data: {
          label: '活动机制核销金额',
          metrics: {
            unit: '万元',
            currentValue: indicators['活动机制核销金额']?.当期值,
            changeValue: indicators['活动机制核销金额']?.变化值,
            changeRate: indicators['活动机制核销金额']?.变化率,
            contribution: indicators['活动机制核销金额']?.贡献度,
          }
        }
      },
      // 活动机制活动ROI（位于下钻指标卡片下方）
      {
        id: 'roi',
        type: 'custom',
        position: { x: 1200, y: 604 }, // 下钻指标y:427 + 177 = 604
        data: {
          label: '活动机制活动ROI',
          metrics: {
            unit: '',
            currentValue: indicators['活动机制活动ROI']?.当期值,
            changeValue: indicators['活动机制活动ROI']?.变化值,
            changeRate: indicators['活动机制活动ROI']?.变化率,
            contribution: indicators['活动机制活动ROI']?.贡献度,
          }
        }
      },
      // 乘号操作符（位于两个指标卡片右侧，稍微向下调整）
      {
        id: 'times',
        type: 'operator',
        position: { x: 1320, y: 500 }, // 向下调整23px
        data: { label: '×' },
        draggable: false,
      }
    ] : []),
  ];

  // 添加机制类型和机制节点（在右侧）
  const mechanismData = getMechanismData();
  const mechanismTypesData = getMechanismTypesData();

  if (selectedDrillDownType === 'mechanisms') {
    // 始终显示机制类型选择（如果有数据）
    if (mechanismTypesData.length > 0) {
      const drillMechanismX = 800; // 下钻机制卡片的x坐标
      const drillMechanismY = 427; // 下钻机制卡片的y坐标
      const horizontalOffset = 350; // 机制类型距离下钻机制的水平距离
      const xPosition = drillMechanismX + horizontalOffset; // 所有机制类型都在右侧

      mechanismTypesData.forEach((mechanismType, index) => {
        // 垂直位置：以下钻机制为中心对称分布
        const centerOffset = (mechanismTypesData.length - 1) / 2;
        const yPosition = drillMechanismY + (index - centerOffset) * 230; // 增加机制类型之间的距离

        // 机制类型节点（在下钻机制右侧对称分布）
        initialNodes.push({
          id: `mechanism_type_${index}`,
          type: 'mechanismType',
          position: { x: xPosition, y: yPosition },
          data: {
            mechanismType: mechanismType.typeName,
            data: mechanismType.data
          },
          draggable: false,
        });
      });
    }

    // 如果选择了机制类型，显示该类型下的具体机制
    if (selectedMechanismType && mechanismData.length > 0) {
      // 第二步：显示选中机制类型下的具体机制
      const mechanismTypeMapping = data?.indicators_data?.['活动GMV']?.活动机制?._mechanism_type_mapping || {};
      const filteredMechanisms = mechanismData.filter(mechanism =>
        mechanismTypeMapping[mechanism.name] === selectedMechanismType
      );

      if (filteredMechanisms.length > 0) {
        // 找到选中的机制类型节点位置
        const selectedTypeIndex = mechanismTypesData.findIndex(type => type.typeName === selectedMechanismType);
        const drillMechanismX = 800;
        const drillMechanismY = 427;
        const horizontalOffset = 350;

        // 计算选中机制类型的位置（现在都在右侧）
        const selectedTypeX = drillMechanismX + horizontalOffset;
        const centerOffset = (mechanismTypesData.length - 1) / 2;
        const selectedTypeY = drillMechanismY + (selectedTypeIndex - centerOffset) * 230; // 与机制类型间距保持一致

        // 具体机制从选中的机制类型延伸出来（继续向右）
        const mechanismHorizontalOffset = 400; // 增加具体机制距离机制类型的距离
        const mechanismBaseX = selectedTypeX + mechanismHorizontalOffset;

        filteredMechanisms.forEach((mechanism, index) => {
          const yPosition = selectedTypeY + (index - (filteredMechanisms.length - 1) / 2) * 250; // 增加具体机制之间的距离

          // 机制节点（从选中的机制类型延伸）
          initialNodes.push({
            id: `mechanism_${index}`,
            type: 'mechanism',
            position: { x: mechanismBaseX, y: yPosition },
            data: {
              label: mechanism.name,
              mechanismName: mechanism.name,
              data: mechanism.data
            },
            draggable: false,
          });

          // 在机制之间添加"+"号（除了最后一个机制）
          if (index < filteredMechanisms.length - 1) {
            const plusYPosition = yPosition + 195; // "+"号位置稍微向下
            const plusXPosition = mechanismBaseX + 120; // "+"号位置稍微向右
            initialNodes.push({
              id: `plus_${index}`,
              type: 'plusSign',
              position: { x: plusXPosition, y: plusYPosition },
              data: {},
              draggable: false,
            });
          }

          // 如果该机制被展开，添加核销金额和ROI详情节点（在机制两侧对称分布）
          if (expandedMechanismDetails[mechanism.name]) {
            const detailHorizontalOffset = 400; // 增加详情节点距离机制节点的距离
            const detailVerticalOffset = 80; // 详情节点的垂直偏移

            // 核销金额详情节点（在机制节点上方）
            initialNodes.push({
              id: `mechanism_subsidy_${index}`,
              type: 'mechanismSubsidy',
              position: {
                x: mechanismBaseX + detailHorizontalOffset,
                y: yPosition - detailVerticalOffset
              },
              data: {
                mechanismName: mechanism.name,
                data: mechanism.data
              },
              draggable: false,
            });

            // ROI详情节点（在机制节点下方）
            initialNodes.push({
              id: `mechanism_roi_${index}`,
              type: 'mechanismRoi',
              position: {
                x: mechanismBaseX + detailHorizontalOffset,
                y: yPosition + detailVerticalOffset
              },
              data: {
                mechanismName: mechanism.name,
                data: mechanism.data
              },
              draggable: false,
            });
          }
        });
      }
    }
  }

  const initialEdges = [
    // 基础连线
    { id: 'e-activity-gmv', source: 'activity_gmv', target: 'gmv', animated: true, style: { strokeWidth: 2 } },
    { id: 'e-natural-gmv', source: 'natural_gmv', target: 'gmv', animated: true, style: { strokeWidth: 2 } },

    // 下钻选项卡片连线：从活动GMV的右侧连接到下钻选项卡片
    ...(showDrillDownOptions ? [
      { id: 'e-activity-drill-indicators', source: 'activity_gmv', sourceHandle: 'drill-down-source', target: 'drill_indicators', animated: true, style: { strokeWidth: 2, stroke: '#1890ff' } },
      { id: 'e-activity-drill-mechanisms', source: 'activity_gmv', sourceHandle: 'drill-down-source', target: 'drill_mechanisms', animated: true, style: { strokeWidth: 2, stroke: '#1890ff' } },
    ] : []),

    // 指标下钻连线：连接到指标卡片的左侧
    ...(selectedDrillDownType === 'indicators' ? [
      { id: 'e-drill-indicators-subsidy', source: 'drill_indicators', sourceHandle: 'indicators-drill-source', target: 'subsidy', targetHandle: 'left', animated: true, style: { strokeWidth: 2, stroke: '#1890ff' } },
      { id: 'e-drill-indicators-roi', source: 'drill_indicators', sourceHandle: 'indicators-drill-source', target: 'roi', targetHandle: 'left', animated: true, style: { strokeWidth: 2, stroke: '#1890ff' } },
    ] : []),
  ];

  // 添加机制类型和机制节点的连线
  if (selectedDrillDownType === 'mechanisms') {
    // 始终显示从下钻机制卡片到机制类型的连线（如果有机制类型数据）
    if (mechanismTypesData.length > 0) {
      mechanismTypesData.forEach((_, index) => {
        initialEdges.push({
          id: `e-drill-mechanisms-type-${index}`,
          source: 'drill_mechanisms',
          sourceHandle: 'mechanisms-drill-source',
          target: `mechanism_type_${index}`,
          animated: true,
          style: { strokeWidth: 2, stroke: '#1890ff' }
        });
      });
    }

    // 如果选择了机制类型，添加从机制类型到具体机制的连线
    if (selectedMechanismType && mechanismData.length > 0) {
      // 第二步：从机制类型到具体机制的连线
      const mechanismTypeMapping = data?.indicators_data?.['活动GMV']?.活动机制?._mechanism_type_mapping || {};
      const filteredMechanisms = mechanismData.filter(mechanism =>
        mechanismTypeMapping[mechanism.name] === selectedMechanismType
      );

      if (filteredMechanisms.length > 0) {
        // 找到选中的机制类型节点ID
        const selectedTypeIndex = mechanismTypesData.findIndex(type => type.typeName === selectedMechanismType);
        const selectedTypeNodeId = `mechanism_type_${selectedTypeIndex}`;

        filteredMechanisms.forEach((mechanism, index) => {
          // 从机制类型到机制节点的连线
          initialEdges.push({
            id: `e-type-mechanism-${index}`,
            source: selectedTypeNodeId,
            sourceHandle: 'mechanism-type-source',
            target: `mechanism_${index}`,
            animated: true,
            style: { strokeWidth: 2, stroke: '#1890ff' }
          });

          // 如果该机制被展开，添加到详情节点的连线
          if (expandedMechanismDetails[mechanism.name]) {
            // 到核销金额详情的连线
            initialEdges.push({
              id: `e-mechanism-subsidy-${index}`,
              source: `mechanism_${index}`,
              target: `mechanism_subsidy_${index}`,
              animated: true,
              style: { strokeWidth: 1, stroke: '#fa8c16' }
            });

            // 到ROI详情的连线
            initialEdges.push({
              id: `e-mechanism-roi-${index}`,
              source: `mechanism_${index}`,
              target: `mechanism_roi_${index}`,
              animated: true,
              style: { strokeWidth: 1, stroke: '#722ed1' }
            });
          }
        });
      }
    }
  }
  
  // 固定容器高度，与供给向保持一致
  const containerHeight = 550;

  return (
    <div style={{
      width: '100%',
      display: 'flex',
      justifyContent: 'center', // 水平居中
      alignItems: 'center', // 垂直居中
      padding: '20px 0', // 添加上下间距
    }}>
      <div style={{
        height: containerHeight,
        width: '2800px', // 固定宽度，不使用minWidth
        border: '1px solid #eee',
        borderRadius: '8px',
        position: 'relative',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)' // 添加阴影效果
      }}>
        <h4 style={{ margin: '16px', fontWeight: 'bold' }}>营销向指标归因分析</h4>
        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative'
        }}>
          <ReactFlow
            nodes={initialNodes}
            edges={initialEdges}
            nodeTypes={nodeTypes}
            fitView
            style={{ width: '100%', height: '100%' }}
          >
            <Background variant="dots" gap={12} size={1} />
          </ReactFlow>
        </div>
      </div>
    </div>
  );
};

// 营销向指标表格组件
const MarketingSideTable = ({ data }) => {
  // 添加下钻状态管理
  const [drillDownLevel, setDrillDownLevel] = useState('indicators'); // 'indicators' | 'mechanismTypes' | 'mechanisms'
  const [selectedMechanismType, setSelectedMechanismType] = useState(null);
  const [drillDownPath, setDrillDownPath] = useState([]);

  // 检查是否有有效数据
  if (!data || !data.indicators_data || Object.keys(data.indicators_data).length === 0) {
    return (
      <div style={{
        padding: '40px 20px',
        textAlign: 'center',
        color: '#999',
        border: '1px dashed #d9d9d9',
        borderRadius: '4px'
      }}>
        暂无营销向指标数据
      </div>
    );
  }

  // --- Re-adding table logic ---
  const indicatorsData = data.indicators_data;

  const formatNumber = (num, suffix = '') => {
    if (num === null || num === undefined || num === '') return '-';

    // 统一处理：先转换为数值，再根据单位格式化
    const numValue = parseFloat(String(num).replace(/,/g, ''));
    if (isNaN(numValue)) return '-';

    if (suffix === '万元') {
      return `${(numValue / 10000).toFixed(2)}${suffix}`;
    } else if (suffix === '%') {
      return `${numValue.toFixed(2)}${suffix}`;
    } else {
      return `${numValue.toLocaleString()}${suffix}`;
    }
  };

  const formatChangeValue = (num, suffix = '') => {
    if (num === null || num === undefined || num === '') return '-';

    // 统一处理：先转换为数值，再根据单位格式化
    const numValue = parseFloat(String(num).replace(/,/g, ''));
    if (isNaN(numValue)) return '-';
    const sign = numValue >= 0 ? '+' : '';

    if (suffix === '万元') {
      return `${sign}${(numValue / 10000).toFixed(2)}${suffix}`;
    } else {
      return `${sign}${numValue.toLocaleString()}${suffix}`;
    }
  };

  const formatChangeRate = (rate) => {
    if (rate === null || rate === undefined || rate === '') return '';
    if (typeof rate === 'string' && rate.includes('%')) {
      return rate;
    }
    const numRate = parseFloat(rate);
    if (isNaN(numRate)) return '';
    const sign = numRate >= 0 ? '+' : '';
    return `${sign}${numRate.toFixed(2)}%`;
  };

  const getChangeColor = (rate) => {
    if (rate === null || rate === undefined || rate === '') return '#333';
    let numRate = 0;
    if (typeof rate === 'string') {
      const cleanRate = rate.replace(/[%,+]/g, '');
      numRate = parseFloat(cleanRate);
    } else {
      numRate = parseFloat(rate);
    }
    if (isNaN(numRate)) return '#333';
    if (numRate === 0) return '#333'; // 变化率为0时显示黑色
    return numRate > 0 ? '#52c41a' : '#f5222d'; // 绿涨红跌
  };

  const indicatorConfig = {
    '自然GMV': { unit: '万元', order: 1 },
    '活动GMV': { unit: '万元', order: 2 },
    '去重活动GMV': { unit: '万元', order: 3 }
  };

  // 兼容性处理：如果活动GMV下还没有活动机制数据，则从活动GMV下钻中获取
  if (indicatorsData['活动GMV下钻'] && indicatorsData['活动GMV下钻'].活动机制) {
    // 只有当活动GMV下没有活动机制数据时，才从活动GMV下钻中移动数据
    if (!indicatorsData['活动GMV'] || !indicatorsData['活动GMV'].活动机制) {
      if (!indicatorsData['活动GMV']) {
        indicatorsData['活动GMV'] = {};
      }
      indicatorsData['活动GMV'].活动机制 = indicatorsData['活动GMV下钻'].活动机制;
    }

    // 将活动GMV下钻数据映射回去重活动GMV指标（仅用于展示，无下钻）
    if (!indicatorsData['去重活动GMV']) {
      indicatorsData['去重活动GMV'] = { ...indicatorsData['活动GMV下钻'] };
      delete indicatorsData['去重活动GMV'].活动机制; // 去重活动GMV不需要活动机制
    }
  }

  // 根据下钻层级构建不同的表格数据
  const buildTableData = () => {
    if (drillDownLevel === 'indicators') {
      // 第一层：显示指标数据
      return Object.keys(indicatorsData)
        .filter(key => indicatorConfig[key])
        .sort((a, b) => (indicatorConfig[a]?.order || 999) - (indicatorConfig[b]?.order || 999))
        .map((indicator) => {
          const d = indicatorsData[indicator];
          const config = indicatorConfig[indicator];
          return {
            key: indicator,
            indicator: indicator,
            currentValue: formatNumber(d.当期值, config.unit),
            baseValue: formatNumber(d.对比期值, config.unit),
            changeValue: formatChangeValue(d.变化值, config.unit),
            changeRate: formatChangeRate(d.变化率),
            contribution: d.贡献度 || '0.00%',
            changeValueRaw: d.变化值,
            changeRateRaw: d.变化率,
            canDrillDown: indicator === '活动GMV' && d.活动机制 && Object.keys(d.活动机制).length > 0
          };
        });
    } else if (drillDownLevel === 'mechanismTypes') {
      // 第二层：显示机制类型数据
      const activityGmv = indicatorsData['活动GMV'];
      if (!activityGmv || !activityGmv.活动机制 || !activityGmv.活动机制._mechanism_types) {
        return [];
      }

      const mechanismTypes = activityGmv.活动机制._mechanism_types;
      return Object.keys(mechanismTypes).map((typeName, index) => {
        const typeData = mechanismTypes[typeName];
        return {
          key: `type-${index}`,
          indicator: typeName,
          currentValue: typeData.当期值 || '0',
          baseValue: typeData.对比期值 || '0',
          changeValue: typeData.变化值 || '0',
          changeRate: typeData.变化率 || '0.00%',
          contribution: typeData.贡献度 || '0.00%',
          canDrillDown: true,
          mechanismType: typeName
        };
      });
    } else if (drillDownLevel === 'mechanisms' && selectedMechanismType) {
      // 第三层：显示选中机制类型下的具体机制
      const activityGmv = indicatorsData['活动GMV'];
      if (!activityGmv || !activityGmv.活动机制) return [];

      const mechanisms = activityGmv.活动机制;
      const mechanismTypeMapping = mechanisms._mechanism_type_mapping || {};

      return Object.keys(mechanisms)
        .filter(name => !name.startsWith('_') && mechanismTypeMapping[name] === selectedMechanismType)
        .map((mechanismName, index) => {
          const m = mechanisms[mechanismName];
          return {
            key: `mechanism-${index}`,
            indicator: mechanismName,
            currentValue: formatNumber(m.当期GMV, '万元'),
            baseValue: formatNumber(m.对比期GMV, '万元'),
            changeValue: formatChangeValue(m.GMV变化值, '万元'),
            changeRate: formatChangeRate(m.变化率),
            contribution: m.贡献度 || '0.00%',
            mechanismType: m.机制类型 || '其他机制'
          };
        });
    }

    return [];
  };

  const tableData = buildTableData();

  // 处理行点击下钻
  const handleRowClick = (record) => {
    if (!record.canDrillDown) return;

    if (drillDownLevel === 'indicators' && record.indicator === '活动GMV') {
      // 从指标层级下钻到机制类型层级
      setDrillDownLevel('mechanismTypes');
      setDrillDownPath([...drillDownPath, { level: 'indicators', value: '活动GMV' }]);
    } else if (drillDownLevel === 'mechanismTypes' && record.mechanismType) {
      // 从机制类型层级下钻到具体机制层级
      setSelectedMechanismType(record.mechanismType);
      setDrillDownLevel('mechanisms');
      setDrillDownPath([...drillDownPath, { level: 'mechanismTypes', value: record.mechanismType }]);
    }
  };

  // 返回上级
  const handleGoBack = () => {
    if (drillDownLevel === 'mechanisms') {
      setDrillDownLevel('mechanismTypes');
      setSelectedMechanismType(null);
      setDrillDownPath(drillDownPath.slice(0, -1));
    } else if (drillDownLevel === 'mechanismTypes') {
      setDrillDownLevel('indicators');
      setDrillDownPath([]);
    }
  };

  // 重置到顶层
  const handleReset = () => {
    setDrillDownLevel('indicators');
    setSelectedMechanismType(null);
    setDrillDownPath([]);
  };

  // 根据下钻层级确定列配置
  const getColumns = () => {
    const baseColumns = [
      {
        title: drillDownLevel === 'indicators' ? '指标名称' : drillDownLevel === 'mechanismTypes' ? '机制类型' : '机制名称',
        dataIndex: 'indicator',
        key: 'indicator',
        width: '20%',
        align: 'center',
        render: (text, record) => (
          <div
            style={{
              fontWeight: '600',
              cursor: record.canDrillDown ? 'pointer' : 'default',
              color: record.canDrillDown ? '#1890ff' : '#333'
            }}
            onClick={() => handleRowClick(record)}
          >
            {text}
            {record.canDrillDown && <ArrowRightOutlined style={{ marginLeft: '8px', fontSize: '12px' }} />}
          </div>
        )
      },
      { title: '当期值', dataIndex: 'currentValue', key: 'currentValue', width: '16%', align: 'center', render: (text, record) => <div style={{ color: getChangeColor(record.changeRate || record.changeRateRaw), fontWeight: '600' }}>{text}</div> },
      { title: '对比期值', dataIndex: 'baseValue', key: 'baseValue', width: '16%', align: 'center', render: (text, record) => <div style={{ color: getChangeColor(record.changeRate || record.changeRateRaw), fontWeight: '600' }}>{text}</div> },
      { title: '变化值', dataIndex: 'changeValue', key: 'changeValue', width: '16%', align: 'center', render: (text, record) => <div style={{ color: getChangeColor(record.changeRate || record.changeRateRaw), fontWeight: '600' }}>{text}</div> },
      { title: '变化率', dataIndex: 'changeRate', key: 'changeRate', width: '16%', align: 'center', render: (text, record) => <div style={{ color: getChangeColor(record.changeRate || record.changeRateRaw), fontWeight: '600' }}>{text}</div> },
      { title: '贡献度', dataIndex: 'contribution', key: 'contribution', width: '16%', align: 'center', render: (text, record) => <div style={{ color: getChangeColor(record.changeRate || record.changeRateRaw), fontWeight: '600' }}>{text}</div> }
    ];

    // 在机制层级添加机制类型列
    if (drillDownLevel === 'mechanisms') {
      baseColumns.splice(1, 0, {
        title: '机制类型',
        dataIndex: 'mechanismType',
        key: 'mechanismType',
        width: '12%',
        align: 'center',
        render: text => <div style={{ fontWeight: '500', textAlign: 'center', color: '#666' }}>{text}</div>
      });
      // 调整其他列的宽度
      baseColumns[0].width = '18%';
      baseColumns.slice(2).forEach(col => col.width = '14%');
    }

    return baseColumns;
  };

  const columns = getColumns();

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center', // 整个营销向内容居中
      width: '100%'
    }}>
      <MarketingSideFlow data={data} />
      <div className="marketing-side-table-container" style={{
        marginTop: '20px',
        width: '100%',
        maxWidth: '2800px' // 与图表宽度保持一致
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h4 style={{ margin: '0', fontSize: '16px', fontWeight: 'bold' }}>
            详细数据
          </h4>

          {/* 面包屑导航 */}
          {drillDownPath.length > 0 && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Breadcrumb>
                <Breadcrumb.Item>
                  <Button type="link" size="small" onClick={handleReset}>
                    指标总览
                  </Button>
                </Breadcrumb.Item>
                {drillDownPath.map((item, index) => (
                  <Breadcrumb.Item key={index}>
                    {index === drillDownPath.length - 1 ? (
                      <span>{item.value}</span>
                    ) : (
                      <Button type="link" size="small" onClick={() => {
                        // 返回到指定层级
                        const targetLevel = index + 1;
                        if (targetLevel === 1) {
                          setDrillDownLevel('mechanismTypes');
                          setSelectedMechanismType(null);
                          setDrillDownPath(drillDownPath.slice(0, 1));
                        }
                      }}>
                        {item.value}
                      </Button>
                    )}
                  </Breadcrumb.Item>
                ))}
              </Breadcrumb>

              <Button
                type="primary"
                size="small"
                icon={<LeftOutlined />}
                onClick={handleGoBack}
                style={{ marginLeft: '16px' }}
              >
                返回上级
              </Button>
            </div>
          )}
        </div>

        <div style={{
          overflowX: 'auto',
          overflowY: 'hidden',
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          background: '#fff'
        }}>
          <Table
            columns={columns}
            dataSource={tableData}
            pagination={false}
            size="middle"
            rowKey="key"
            bordered
            style={{
              background: '#fff',
              minWidth: '800px' // 设置最小宽度确保表格不会过度压缩
            }}
            scroll={false} // 禁用Table自身的滚动，让外层容器处理
            onRow={(record) => ({
              style: {
                color: getChangeColor(record.changeRate || record.changeRateRaw)
              }
            })}
          />
        </div>
      </div>
    </div>
  );
};

// 指标拆解弹窗组件
const MetricsDecompositionModal = ({
  visible,
  onClose,
  selectedValue,
  selectedDimension,
  data,
  drillDownPath = [],
  drillDownData = {},
  isActivityGMV = false,
  attributionSettings = null,
  selectedAnalysisDimensions = [],
  // 新增日期参数
  dateParams = null,
  // 新增筛选条件参数
  filterConditions = null
}) => {
  const [loading, setLoading] = useState(false);
  const [decompositionData, setDecompositionData] = useState(null);
  const [activeTab, setActiveTab] = useState('marketing'); // 默认显示营销向



  const fetchDecompositionData = useCallback(async () => {
    setLoading(true);
    try {
      // 参考 drill_down 接口的实现方式，构建完整的请求参数
      const requestData = new FormData();

      // 移除selected_dimension及selected_value，改为根据下钻路径与当前点击的维度构造参数
      // 构建前端维度到接口字段的映射（参考drill_down接口的映射方式）
      const dimensionParamMap = {
        '平台': 'platform',
        '省份': 'province',
        // 使用 standard_city 以符合后端接口要求
        '城市': 'standard_city',
        '零售商': 'retailer',
        '子品牌': 'sub_brand',
        '商品': 'product_name',  // 参考drill_down接口，商品映射到product_name
        '商品名称': 'product_name',  // 商品名称也映射到product_name
        '券机制': 'coupon_mechanism',
        '券门槛': 'coupon_threshold',
        '优惠力度': 'coupon_discount'
      };


      
      // 获取基础筛选条件 - 优先使用传递的filterConditions，否则从URL参数中获取
      const urlParams = new URLSearchParams(window.location.search);

      // 基础参数
      const brand = urlParams.get('brand');
      if (brand) requestData.append('brand', brand);

      // 使用传递的筛选条件或从URL参数获取
      const subBrand = filterConditions?.subBrand || urlParams.get('sub_brand') || '全部';
      requestData.append('sub_brand', subBrand);

      const province = filterConditions?.province || urlParams.get('province') || '全部';
      requestData.append('province', province);

      const city = filterConditions?.city || urlParams.get('city') || '全部';
      requestData.append('city', city);

      const retailer = filterConditions?.retailer || urlParams.get('retailer') || '全部';
      requestData.append('retailer', retailer);

      const platform = filterConditions?.platform || urlParams.get('platform') || '全部';
      requestData.append('platform', platform);

      const upc = filterConditions?.upc || urlParams.get('upc') || '全部';
      requestData.append('upc', upc);

      // 添加活动GMV相关的筛选条件
      if (filterConditions?.couponMechanism) {
        requestData.append('coupon_mechanism', filterConditions.couponMechanism);
      }
      if (filterConditions?.couponThreshold) {
        requestData.append('coupon_threshold', filterConditions.couponThreshold);
      }
      if (filterConditions?.couponDiscount) {
        requestData.append('coupon_discount', filterConditions.couponDiscount);
      }
      
      // 根据下钻路径和当前选中的维度值覆盖相应参数
      if (Array.isArray(drillDownPath)) {
        drillDownPath.forEach(item => {
          // 处理交叉维度数据
          if (item.crossDimensionData && typeof item.crossDimensionData === 'object') {
            // 如果有交叉维度数据，优先使用交叉维度数据
            Object.keys(item.crossDimensionData).forEach(crossKey => {
              const apiKey = dimensionParamMap[crossKey] || crossKey;
              if (apiKey && item.crossDimensionData[crossKey] && item.crossDimensionData[crossKey] !== '全部') {
                let crossValue = item.crossDimensionData[crossKey];

                // 如果是商品维度，需要同时设置product_name和upc参数
                if ((crossKey === '商品' || crossKey === '商品名称') && crossValue !== '全部') {
                  // 设置商品名称
                  requestData.set(apiKey, crossValue);

                  // 查找并设置对应的UPC码
                  let upcFound = false;

                  // 如果有下钻数据，优先从下钻数据中查找
                  if (drillDownData && typeof drillDownData === 'object' && drillDownData.sheets_data) {
                    Object.entries(drillDownData.sheets_data).forEach(([sheetName, sheetData]) => {
                      if (Array.isArray(sheetData) && !upcFound) {
                        const productRow = sheetData.find(row =>
                          row['商品名称'] === crossValue || row['商品'] === crossValue
                        );
                        if (productRow) {
                          const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                          if (upcValue) {
                            requestData.set('upc', upcValue);
                            upcFound = true;
                          }
                        }
                      }
                    });
                  }

                  // 如果下钻数据中没找到，再从当前数据中查找
                  if (!upcFound && data && Array.isArray(data)) {
                    const productRow = data.find(row =>
                      row['商品名称'] === crossValue || row['商品'] === crossValue
                    );
                    if (productRow) {
                      // 尝试多种UPC字段名称（大写UPC、小写upc）
                      const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                      if (upcValue) {
                        requestData.set('upc', upcValue);
                        upcFound = true;
                      }
                    }
                  }
                } else {
                  requestData.set(apiKey, crossValue);
                }
              }
            });
          }

          // 处理普通下钻路径
          const key = dimensionParamMap[item.dimension] || item.dimension;
          if (key) {
            let value = item.value;

            // 如果是商品维度，需要同时设置product_name和upc参数
            if ((item.dimension === '商品' || item.dimension === '商品名称') && value !== '全部') {
              // 设置商品名称
              requestData.set(key, value);

              // 如果下钻路径中有UPC信息，直接使用
              if (item.upc) {
                requestData.set('upc', item.upc);
              } else {
                // 否则从数据中查找对应的UPC码
                let upcFound = false;

                // 首先尝试从下钻数据中查找UPC
                if (drillDownData && typeof drillDownData === 'object' && drillDownData.sheets_data) {
                  Object.entries(drillDownData.sheets_data).forEach(([sheetName, sheetData]) => {
                    if (Array.isArray(sheetData) && !upcFound) {
                      const productRow = sheetData.find(row =>
                        row['商品名称'] === value || row['商品'] === value
                      );
                      if (productRow) {
                        const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                        if (upcValue) {
                          requestData.set('upc', upcValue);
                          upcFound = true;
                        }
                      }
                    }
                  });
                }

                // 如果下钻数据中没找到，再从当前数据中查找
                if (!upcFound && data && Array.isArray(data)) {
                  const productRow = data.find(row =>
                    row['商品名称'] === value || row['商品'] === value
                  );
                  if (productRow) {
                    // 尝试多种UPC字段名称（大写UPC、小写upc）
                    const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                    if (upcValue) {
                      requestData.set('upc', upcValue);
                      upcFound = true;
                    }
                  }
                }
              }
            } else {
              requestData.set(key, value);
            }
          }
        });
      }
      // 处理当前选中的维度值
      if (selectedDimension && selectedValue !== undefined) {
        // 如果选中的维度是交叉维度（如"平台-省份-城市"），需要拆分并写入各自字段
        if (selectedDimension.includes('-') && typeof selectedValue === 'string' && selectedValue.includes('-')) {
          const dimParts = selectedDimension.split('-');
          const valParts = selectedValue.split('-');
          dimParts.forEach((dim, idx) => {
            const cleanDim = dim.trim();
            let cleanVal = (valParts[idx] || '全部').trim();
            const paramKey = dimensionParamMap[cleanDim] || cleanDim;

            // 如果是商品维度，需要同时设置product_name和upc参数
            if ((cleanDim === '商品' || cleanDim === '商品名称') && cleanVal !== '全部') {
              // 设置商品名称
              requestData.set(paramKey, cleanVal);

              // 从data中查找对应的UPC码并设置upc参数
              let upcFound = false;

              // 首先尝试从下钻数据中查找UPC
              if (drillDownData && typeof drillDownData === 'object' && drillDownData.sheets_data) {
                Object.values(drillDownData.sheets_data).forEach(sheetData => {
                  if (Array.isArray(sheetData) && !upcFound) {
                    const productRow = sheetData.find(row =>
                      row['商品名称'] === cleanVal || row['商品'] === cleanVal
                    );
                    if (productRow) {
                      const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                      if (upcValue) {
                        requestData.set('upc', upcValue);
                        upcFound = true;
                      }
                    }
                  }
                });
              }

              // 如果下钻数据中没找到，再从当前数据中查找
              if (!upcFound && data && Array.isArray(data)) {
                const productRow = data.find(row =>
                  row['商品名称'] === cleanVal || row['商品'] === cleanVal
                );
                if (productRow) {
                  // 尝试多种UPC字段名称（大写UPC、小写upc）
                  const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                  if (upcValue) {
                    requestData.set('upc', upcValue);
                  }
                }
              }
              return; // 跳过后面的requestData.set，因为已经设置过了
            }

            requestData.set(paramKey, cleanVal);
          });
          // 删除组合维度字段，避免后端无法识别
          requestData.delete(selectedDimension);
        } else {
          // 处理单个维度
          const key = dimensionParamMap[selectedDimension] || selectedDimension;
          if (key) {
            let value = selectedValue;

            // 如果是商品维度，需要同时设置product_name和upc参数
            if ((selectedDimension === '商品' || selectedDimension === '商品名称') && value !== '全部') {
              // 设置商品名称
              requestData.set(key, value);

              // 从数据中查找对应的UPC码并设置upc参数
              let upcFound = false;

              // 首先尝试从下钻数据中查找UPC
              if (drillDownData && typeof drillDownData === 'object') {
                // 检查是否有 sheets_data 属性
                if (drillDownData.sheets_data) {
                  Object.entries(drillDownData.sheets_data).forEach(([sheetName, sheetData]) => {
                    if (Array.isArray(sheetData) && !upcFound) {
                      const productRow = sheetData.find(row =>
                        row['商品名称'] === value || row['商品'] === value
                      );
                      if (productRow) {
                        const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                        if (upcValue) {
                          requestData.set('upc', upcValue);
                          upcFound = true;
                        }
                      }
                    }
                  });
                } else {
                  // 检查是否直接包含商品数据（下钻数据的结构可能不同）
                  Object.entries(drillDownData).forEach(([key, data]) => {
                    // 检查是否有 sheets_data 结构
                    if (data && data.sheets_data && !upcFound) {
                      Object.entries(data.sheets_data).forEach(([sheetName, sheetData]) => {
                        if (Array.isArray(sheetData) && !upcFound) {
                          const productRow = sheetData.find(row =>
                            row['商品名称'] === value || row['商品'] === value
                          );
                          if (productRow) {
                            const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                            if (upcValue) {
                              requestData.set('upc', upcValue);
                              upcFound = true;
                            }
                          }
                        }
                      });
                    }
                    // 检查是否直接是商品数据数组
                    else if (Array.isArray(data) && !upcFound) {
                      const productRow = data.find(row =>
                        row['商品名称'] === value || row['商品'] === value
                      );
                      if (productRow) {
                        const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                        if (upcValue) {
                          requestData.set('upc', upcValue);
                          upcFound = true;
                        }
                      }
                    }
                  });
                }
              }

              // 如果下钻数据中没找到，再从当前数据中查找
              if (!upcFound && data && Array.isArray(data)) {
                const productRow = data.find(row =>
                  row['商品名称'] === value || row['商品'] === value
                );
                if (productRow) {
                  // 尝试多种UPC字段名称（大写UPC、小写upc）
                  const upcValue = productRow.UPC || productRow.upc || productRow['UPC'] || productRow['upc'];
                  if (upcValue) {
                    requestData.set('upc', upcValue);
                    upcFound = true;
                  }
                }
              }
            } else {
              requestData.set(key, value);
            }
          }
        }
      }
      
      // compare_detail_type 参数（从 URL 或 localStorage 获取）
      const compareDetailType = urlParams.get('compare_detail_type') || localStorage.getItem('compare_detail_type');
      if (compareDetailType) {
        requestData.append('compare_detail_type', compareDetailType);
      }
      
      // 活动GMV相关参数
      const couponMechanism = urlParams.get('coupon_mechanism') || '全部';
      requestData.append('coupon_mechanism', couponMechanism);

      const couponThreshold = urlParams.get('coupon_threshold') || '全部';
      requestData.append('coupon_threshold', couponThreshold);

      const couponDiscount = urlParams.get('coupon_discount') || '全部';
      requestData.append('coupon_discount', couponDiscount);

      // 归因设置相关参数
      const attrIndex = urlParams.get('attr_index') ||
                       localStorage.getItem('attr_index') ||
                       '全量GMV';
      requestData.append('attr_index', attrIndex);

      // 在指标拆解弹窗中，总是请求两种类型的数据以支持切换
      requestData.append('indicator_types', '营销向,供给向');

      const attributionMethods = urlParams.get('attribution_methods') ||
                                localStorage.getItem('attribution_methods') ||
                                '维度,指标';
      requestData.append('attribution_methods', attributionMethods);

      // 构建当前场景下的分析维度：基于下钻路径和当前选中维度
      let analysisDimensions = '';
      const currentDimensions = [];

      // 1. 从下钻路径中提取维度（保持中文维度名称）
      if (Array.isArray(drillDownPath) && drillDownPath.length > 0) {
        drillDownPath.forEach(item => {
          // 处理交叉维度数据
          if (item.crossDimensionData && typeof item.crossDimensionData === 'object') {
            Object.keys(item.crossDimensionData).forEach(crossKey => {
              if (!currentDimensions.includes(crossKey)) {
                currentDimensions.push(crossKey);
              }
            });
          }

          // 处理普通下钻维度
          if (item.dimension && !currentDimensions.includes(item.dimension)) {
            currentDimensions.push(item.dimension);
          }
        });
      }

      // 2. 添加当前选中的维度
      if (selectedDimension) {
        // 如果是交叉维度（包含"-"），需要拆分
        if (selectedDimension.includes('-')) {
          const dimParts = selectedDimension.split('-');
          dimParts.forEach(dim => {
            const cleanDim = dim.trim();
            if (!currentDimensions.includes(cleanDim)) {
              currentDimensions.push(cleanDim);
            }
          });
        } else {
          if (!currentDimensions.includes(selectedDimension)) {
            currentDimensions.push(selectedDimension);
          }
        }
      }

      // 3. 如果没有从下钻路径和当前维度中获取到维度，回退到原有逻辑
      if (currentDimensions.length === 0) {
        if (selectedAnalysisDimensions && selectedAnalysisDimensions.length > 0) {
          // 直接使用前端维度名称（中文）
          analysisDimensions = Array.isArray(selectedAnalysisDimensions)
            ? selectedAnalysisDimensions.join(',')
            : selectedAnalysisDimensions;
        } else {
          // 最后回退到URL或localStorage
          analysisDimensions = urlParams.get('analysis_dimensions') ||
                             localStorage.getItem('analysis_dimensions') ||
                             '';
        }
      } else {
        // 使用中文维度名称
        analysisDimensions = currentDimensions.join(',');
      }

    

      if (analysisDimensions) {
        requestData.append('analysis_dimensions', analysisDimensions);
        // target_dimension 参数：应该传递归因设置里的分析维度，格式参考get_result_data中的analysis_dimensions
        requestData.append('target_dimension', analysisDimensions);
      }

      // 处理日期参数 - 参考 drill_down 接口的日期处理逻辑
      if (dateParams) {
        
        // 参考 drill_down 接口的日期处理逻辑
        if (dateParams.dateType === 'single' && dateParams.tarDate) {
          // 检查是否是月或年维度
          if (dateParams.modalDateType === '月' || dateParams.modalDateType === '年') {
            if (dateParams.modalDateType === '月') {
              requestData.append('tar_start_date', dateParams.tarDate.startOf('month').format('YYYYMMDD'));
              requestData.append('tar_end_date', dateParams.tarDate.endOf('month').format('YYYYMMDD'));
              if (dateParams.baseStartDate) {
                requestData.append('base_start_date', dateParams.baseStartDate.startOf('month').format('YYYYMMDD'));
                requestData.append('base_end_date', dateParams.baseStartDate.endOf('month').format('YYYYMMDD'));
              }
            } else {
              requestData.append('tar_start_date', dateParams.tarDate.startOf('year').format('YYYYMMDD'));
              requestData.append('tar_end_date', dateParams.tarDate.endOf('year').format('YYYYMMDD'));
              if (dateParams.baseStartDate) {
                requestData.append('base_start_date', dateParams.baseStartDate.startOf('year').format('YYYYMMDD'));
                requestData.append('base_end_date', dateParams.baseStartDate.endOf('year').format('YYYYMMDD'));
              }
            }
          } else {
            // 单日期模式
            requestData.append('tar_date', dateParams.tarDate.format('YYYYMMDD'));
            if (dateParams.baseStartDate) {
              requestData.append('base_date', dateParams.baseStartDate.format('YYYYMMDD'));
            }
          }
        } else if (dateParams.dateType === 'range' && dateParams.tarStartDate && dateParams.tarEndDate && dateParams.baseStartDate && dateParams.baseEndDate) {
          // 日期范围模式
          requestData.append('tar_start_date', dateParams.tarStartDate.format('YYYYMMDD'));
          requestData.append('tar_end_date', dateParams.tarEndDate.format('YYYYMMDD'));
          requestData.append('base_start_date', dateParams.baseStartDate.format('YYYYMMDD'));
          requestData.append('base_end_date', dateParams.baseEndDate.format('YYYYMMDD'));
        } else {
          const today = new Date();
          const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
          const todayFormatted = today.getFullYear() + String(today.getMonth() + 1).padStart(2, '0') + String(today.getDate()).padStart(2, '0');
          const yesterdayFormatted = yesterday.getFullYear() + String(yesterday.getMonth() + 1).padStart(2, '0') + String(yesterday.getDate()).padStart(2, '0');
          requestData.append('tar_date', todayFormatted);
          requestData.append('base_date', yesterdayFormatted);
        }
      } else {
        // 如果没有传入日期参数，尝试从 URL 参数中获取
        let tarDate = urlParams.get('tar_date');
        let baseDate = urlParams.get('base_date');
        let tarStartDate = urlParams.get('tar_start_date');
        let tarEndDate = urlParams.get('tar_end_date');
        let baseStartDate = urlParams.get('base_start_date');
        let baseEndDate = urlParams.get('base_end_date');
        

        
        // 判断日期类型：如果有 start_date 和 end_date，说明是日期范围模式
        const isDateRange = !!(tarStartDate && tarEndDate && baseStartDate && baseEndDate);
        const isSingleDate = !!(tarDate && baseDate);
        
        if (isDateRange) {
          requestData.append('tar_start_date', tarStartDate);
          requestData.append('tar_end_date', tarEndDate);
          requestData.append('base_start_date', baseStartDate);
          requestData.append('base_end_date', baseEndDate);
        } else if (isSingleDate) {
          requestData.append('tar_date', tarDate);
          requestData.append('base_date', baseDate);
        } else {
          const today = new Date();
          const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
          const todayFormatted = today.getFullYear() + String(today.getMonth() + 1).padStart(2, '0') + String(today.getDate()).padStart(2, '0');
          const yesterdayFormatted = yesterday.getFullYear() + String(yesterday.getMonth() + 1).padStart(2, '0') + String(yesterday.getDate()).padStart(2, '0');
          requestData.append('tar_date', todayFormatted);
          requestData.append('base_date', yesterdayFormatted);
        }
      }



      // 发送请求 - 使用 GET 方法，将 FormData 转换为查询参数
      const queryParams = new URLSearchParams();
      for (const [key, value] of requestData.entries()) {
        queryParams.append(key, value);
      }
      
      const API_BASE_URL = process.env.NODE_ENV === 'production'
        ? '/api'  // 生产环境使用相对路径，通过nginx转发
        : 'http://localhost:5003';
      
      const response = await fetch(`${API_BASE_URL}/get_metrics_decomposition?${queryParams}`);
      const result = await response.json();
      
      if (result.status === 'success') {
        setDecompositionData(result);
      } else {
        message.error(result.message || '获取指标拆解数据失败');
      }
    } catch (error) {
      message.error('获取指标拆解数据失败');
    } finally {
      setLoading(false);
    }
  }, [selectedValue, selectedDimension, drillDownPath, data, dateParams, drillDownData, filterConditions]);

  // 当模态框打开时获取数据
  useEffect(() => {
    if (visible && selectedValue && selectedDimension) {
      fetchDecompositionData();
    }
  }, [visible, selectedValue, selectedDimension, drillDownPath, fetchDecompositionData]);

  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" tip="加载中..." />
        </div>
      );
    }

    if (!decompositionData) {
      return <div style={{ textAlign: 'center', padding: '20px' }}>暂无数据</div>;
    }

    const items = [];

    // 添加营销向标签页
    if (decompositionData.marketing_side_data) {
      const marketingData = decompositionData.marketing_side_data;
      items.push({
        key: 'marketing',
        label: '营销向指标',
        children: marketingData.message ? (
          <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
            {marketingData.message}
          </div>
        ) : (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            width: '100%'
          }}>
            <MarketingSideFlow data={marketingData} />
          </div>
        )
      });
    }

    // 添加供给向标签页
    if (decompositionData.supply_side_data || decompositionData.supply_side_attribution) {
      // 兼容两种数据字段名
      const supplyData = decompositionData.supply_side_data || decompositionData.supply_side_attribution;
      items.push({
        key: 'supply',
        label: '供给向指标',
        children: supplyData.message ? (
          <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
            {supplyData.message}
          </div>
        ) : (
          <div>
            <SupplySideFlow data={supplyData} />
          </div>
        )
      });
    }

    if (items.length === 0) {
      return <div style={{ textAlign: 'center', padding: '20px' }}>暂无可展示的指标数据</div>;
    }

    return (
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={items}
        style={{ marginTop: '20px' }}
      />
    );
  };

  return (
    <Modal
      title={
        <div>
          <span>指标拆解 - {selectedDimension}: {selectedValue}</span>
        </div>
      }
      visible={visible}
      onCancel={onClose}
      width={1400}
      footer={null}
      destroyOnClose
    >
      {renderContent()}
    </Modal>
  );
};

// 整体TOP3数据展示组件
const Top3Content = ({ data, dimension }) => {
  if (!data) return null;

  // 从表格数据中提取并计算 TOP3
  const calculateTop3 = (tableData, currentDimension) => {
    if (!Array.isArray(tableData) || tableData.length === 0) return { positive: [], negative: [] };
    
    // 过滤掉下钻数据，只使用原始数据计算Top3
    const originalData = tableData.filter(item => !item._isDrillDown);

    // 判断是否为活动GMV数据
    const isActivityGMV = tableData.some(item => '活动GMV贡献度' in item);

    // 根据贡献度排序（只使用原始数据）
    const sortedData = originalData.map(item => {
      // 根据不同维度获取正确的名称字段
      let name;
      if (currentDimension === '商品') {
        name = item['商品'] || item['商品名称'];
      } else if (currentDimension === '省份') {
        name = item['省份'] || item['区域'];
      } else if (currentDimension === '城市') {
        name = item['城市'];
      } else if (currentDimension === '平台') {
        name = item['平台'];
      } else if (currentDimension === '零售商') {
        name = item['零售商'];
      } else if (currentDimension === '子品牌') {
        name = item['子品牌'];
      } else if (currentDimension === '券机制') {
        name = item['券机制'];
      } else if (currentDimension === '券门槛') {
        name = item['券门槛'];
      } else if (currentDimension === '优惠力度') {
        name = item['优惠力度'];
      } else if (item['交叉维度名称']) {
        // 如果是交叉维度，使用交叉维度名称
        name = item['交叉维度名称'];
      } else {
        name = item[currentDimension] || item['名称'];
      }
      
      // 根据数据类型选择正确的字段
      const contributionField = isActivityGMV ? '活动GMV贡献度' : 'GMV贡献度';
      const targetGMVField = isActivityGMV ? '当期活动GMV' : '当期GMV';
      const changeValueField = isActivityGMV ? '活动GMV变化值' : 'GMV变化值';
      const changeRateField = isActivityGMV ? '活动GMV变化率' : 'GMV变化率';
      
      // 提取各项数据
      const contribution = parseFloat((item[contributionField] || '0%').replace('%', ''));
      const targetGMV = item[targetGMVField] || 0;
      const changeValue = item[changeValueField] || 0;
      const changeRate = parseFloat((item[changeRateField] || '0%').replace('%', ''));
      
      return { 
        name, 
        contribution, 
        targetGMV, 
        changeValue, 
        changeRate 
      };
    });

    // 分离正向和负向数据
    const positiveData = sortedData
      .filter(item => item.contribution > 0)
      .sort((a, b) => b.contribution - a.contribution)
      .slice(0, 3);

    const negativeData = sortedData
      .filter(item => item.contribution < 0)
      .sort((a, b) => a.contribution - b.contribution)
      .slice(0, 3);

    return {
      positive: positiveData,
      negative: negativeData
    };
  };

  // 格式化数字的辅助函数
  const formatNumber = (num) => {
    if (num === null || num === undefined || num === '-') return '0';
    const number = parseFloat(num);
    if (isNaN(number)) return '0';
    return Math.round(number).toLocaleString('zh-CN');
  };

  // 格式化变化值的辅助函数
  const formatChangeValue = (num) => {
    if (num === null || num === undefined || num === '-') return '0';
    const number = parseFloat(num);
    if (isNaN(number)) return '0';
    const formatted = Math.round(number).toLocaleString('zh-CN');
    return number >= 0 ? `+${formatted}` : formatted;
  };

  // 格式化变化率的辅助函数
  const formatChangeRate = (num) => {
    if (num === null || num === undefined || isNaN(num)) return '0.00%';
    return num >= 0 ? `+${num.toFixed(2)}%` : `${num.toFixed(2)}%`;
  };

  // 格式化贡献度的辅助函数
  const formatContribution = (num) => {
    if (num === null || num === undefined || isNaN(num)) return '0.00%';
    return num >= 0 ? `+${num.toFixed(2)}%` : `${num.toFixed(2)}%`;
  };

  // 如果是整体维度，综合所有其他维度的数据
  if (dimension === '整体') {
    // 获取所有维度的Top3
    const allPositive = [];
    const allNegative = [];
    
    // 判断是否为活动GMV数据
    const isActivityGMV = Object.keys(data).some(key => 
      data[key] && Array.isArray(data[key]) && data[key].length > 0 && '活动GMV贡献度' in data[key][0]
    );
    
    // 根据数据类型确定要分析的维度
    const dimensions = isActivityGMV 
      ? ['省份', '城市', '平台', '零售商', '子品牌', '券机制', '券门槛', '优惠力度'] 
      : ['商品', '省份', '城市', '平台', '零售商', '子品牌'];
    
    // 收集所有维度的Top3数据
    dimensions.forEach(dim => {
      if (data[dim] && Array.isArray(data[dim])) {
        const { positive, negative } = calculateTop3(data[dim], dim);
        
        positive.forEach(item => {
          allPositive.push({...item, dimension: dim});
        });
        
        negative.forEach(item => {
          allNegative.push({...item, dimension: dim});
        });
      }
    });
    
    // 重新排序并只取全局Top3
    const topPositive = allPositive
      .sort((a, b) => b.contribution - a.contribution)
      .slice(0, 3);
      
    const topNegative = allNegative
      .sort((a, b) => a.contribution - b.contribution)
      .slice(0, 3);
      
    return (
      <div style={{ padding: '10px' }}>
        <div style={{ marginBottom: '20px' }}>
          <Title level={5} style={{ color: '#52c41a', marginBottom: '10px' }}>正向贡献Top3</Title>
          {topPositive.length > 0 ? (
            topPositive.map((item, index) => (
              <div key={`positive-${index}`} style={{ marginBottom: '10px', fontSize: '14px', lineHeight: '1.5' }}>
                <span>
                  {`${index + 1}. ${item.name || '未知'}（${item.dimension === '省份' ? '省份' : 
                                        item.dimension === '城市' ? '城市' :
                                        item.dimension === '平台' ? '平台' :
                                        item.dimension === '零售商' ? '零售商' :
                                        item.dimension === '子品牌' ? '子品牌' :
                                        item.dimension === '商品' ? '商品' : 
                                        item.dimension === '券机制' ? '券机制' :
                                        item.dimension === '券门槛' ? '券门槛' :
                                        item.dimension === '优惠力度' ? '优惠力度' : '整体'}）：`}
                  <span style={{ color: '#52c41a' }}>
                    {`当期GMV: ${formatNumber(item.targetGMV)}，GMV增长${formatChangeValue(item.changeValue)}，GMV增长率${formatChangeRate(item.changeRate)}，对整体增长的贡献度${formatContribution(item.contribution)}`}
                  </span>
                </span>
              </div>
            ))
          ) : (
            <div>暂无正向贡献数据</div>
          )}
        </div>
        
        <div>
          <Title level={5} style={{ color: '#cf1322', marginBottom: '10px' }}>负向贡献Top3</Title>
          {topNegative.length > 0 ? (
            topNegative.map((item, index) => (
              <div key={`negative-${index}`} style={{ marginBottom: '10px', fontSize: '14px', lineHeight: '1.5' }}>
                <span>
                  {`${index + 1}. ${item.name || '未知'}（${item.dimension === '省份' ? '省份' : 
                                        item.dimension === '城市' ? '城市' :
                                        item.dimension === '平台' ? '平台' :
                                        item.dimension === '零售商' ? '零售商' :
                                        item.dimension === '子品牌' ? '子品牌' :
                                        item.dimension === '商品' ? '商品' : 
                                        item.dimension === '券机制' ? '券机制' :
                                        item.dimension === '券门槛' ? '券门槛' :
                                        item.dimension === '优惠力度' ? '优惠力度' : '整体'}）：`}
                  <span style={{ color: '#cf1322' }}>
                    {`当期GMV: ${formatNumber(item.targetGMV)}，GMV增长${formatChangeValue(item.changeValue)}，GMV增长率${formatChangeRate(item.changeRate)}，对整体增长的贡献度${formatContribution(item.contribution)}`}
                  </span>
                </span>
              </div>
            ))
          ) : (
            <div>暂无负向贡献数据</div>
          )}
        </div>
      </div>
    );
  }

  // 如果不是整体维度，使用原有逻辑处理单一维度
  if (!data[dimension]) return null;
  
  const { positive, negative } = calculateTop3(data[dimension], dimension);

  return (
    <div style={{ padding: '10px' }}>
      <div style={{ marginBottom: '20px' }}>
        <Title level={5} style={{ color: '#52c41a', marginBottom: '10px' }}>正向贡献Top3</Title>
        {positive.length > 0 ? (
          positive.map((item, index) => (
            <div key={`positive-${index}`} style={{ marginBottom: '10px', fontSize: '14px', lineHeight: '1.5' }}>
              <span>
                {`${index + 1}. ${item.name || '未知'}（${dimension === '省份' ? '省份' : 
                                        dimension === '城市' ? '城市' :
                                        dimension === '平台' ? '平台' :
                                        dimension === '零售商' ? '零售商' :
                                        dimension === '子品牌' ? '子品牌' :
                                        dimension === '商品' ? '商品' : 
                                        dimension === '券机制' ? '券机制' :
                                        dimension === '券门槛' ? '券门槛' :
                                        dimension === '优惠力度' ? '优惠力度' : '整体'}）：`}
                <span style={{ color: '#52c41a' }}>
                  {`当期GMV: ${formatNumber(item.targetGMV)}，GMV增长${formatChangeValue(item.changeValue)}，GMV增长率${formatChangeRate(item.changeRate)}，对整体增长的贡献度${formatContribution(item.contribution)}`}
                </span>
              </span>
            </div>
          ))
        ) : (
          <div>暂无正向贡献数据</div>
        )}
      </div>
      
      <div>
        <Title level={5} style={{ color: '#cf1322', marginBottom: '10px' }}>负向贡献Top3</Title>
        {negative.length > 0 ? (
          negative.map((item, index) => (
            <div key={`negative-${index}`} style={{ marginBottom: '10px', fontSize: '14px', lineHeight: '1.5' }}>
              <span>
                {`${index + 1}. ${item.name || '未知'}（${dimension === '省份' ? '省份' : 
                                        dimension === '城市' ? '城市' :
                                        dimension === '平台' ? '平台' :
                                        dimension === '零售商' ? '零售商' :
                                        dimension === '子品牌' ? '子品牌' :
                                        dimension === '商品' ? '商品' : 
                                        dimension === '券机制' ? '券机制' :
                                        dimension === '券门槛' ? '券门槛' :
                                        dimension === '优惠力度' ? '优惠力度' : '整体'}）：`}
                <span style={{ color: '#cf1322' }}>
                  {`当期GMV: ${formatNumber(item.targetGMV)}，GMV增长${formatChangeValue(item.changeValue)}，GMV增长率${formatChangeRate(item.changeRate)}，对整体增长的贡献度${formatContribution(item.contribution)}`}
                </span>
              </span>
            </div>
          ))
        ) : (
          <div>暂无负向贡献数据</div>
        )}
      </div>
    </div>
  );
};



// 供给向指标的容器组件
const SupplySideSection = ({ data }) => {
  return (
    <div>
      <SupplySideFlow data={data} />
      <SupplySideMetricsTable data={data} />
    </div>
  );
};

// 表格内容组件
const TableContent = React.memo(({
  data,
  dimension,
  isCrossDimension = false,
  selectedAnalysisDimensions = [], // 所有可选维度
  currentDimension = '', // 当前维度
  onDrillDown = null, // 下钻回调

  drillDownPath = [], // 下钻路径
  onGoBack = null, // 返回上级回调
  onReset = null, // 重置回调
  onBreadcrumbClick = null, // 面包屑点击回调

  drillDownData = {}, // 下钻数据
  attributionSettings = null, // 归因设置信息
  // 新增日期参数
  dateParams = null,
  // 新增筛选条件参数
  filterConditions = null
}) => {
  // 添加分页状态 - 确保Hooks在所有条件判断之前调用
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  // 指标拆解弹窗状态
  const [metricsModalVisible, setMetricsModalVisible] = useState(false);
  const [selectedMetricsValue, setSelectedMetricsValue] = useState('');
  const [selectedMetricsDimension, setSelectedMetricsDimension] = useState('');

  // 获取下钻选项（支持多级连续下钻）- 使用useCallback缓存，必须在早期返回之前调用
  const getDrillDownOptions = useCallback((clickedValue, record) => {
    if (!selectedAnalysisDimensions || selectedAnalysisDimensions.length === 0) return [];
    
    // 获取已经下钻的维度（从下钻路径中获取）
    const drilledDimensions = drillDownPath ? drillDownPath.map(item => item.dimension) : [];
    
    // 确定当前实际显示的维度
    let actualCurrentDimension = currentDimension;
    
    if (drillDownPath && drillDownPath.length > 0) {
      // 从下钻数据键中推断当前显示的维度
      if (drillDownData && typeof drillDownData === 'object') {
        const drillKeys = Object.keys(drillDownData);
        if (drillKeys.length > 0) {
          const pathString = drillDownPath.map(p => `${p.dimension}:${p.value}`).join('|');
          const matchingKey = drillKeys.find(key => key.includes(pathString));
          if (matchingKey) {
            // 从键中提取目标维度 (格式: pathString_targetDimension)
            const parts = matchingKey.split('_');
            actualCurrentDimension = parts[parts.length - 1]; // 最后一部分是目标维度
          }
        }
      }
    }
    
    // 标准化维度名称的函数：商品和商品名称视为同一个维度
    const normalizeDimension = (dim) => {
      if (dim === '商品' || dim === '商品名称') return '商品';
      return dim;
    };
    
    // 初始化排除的维度：包括已经下钻的维度和当前显示的维度
    let excludedDimensions = [...drilledDimensions, actualCurrentDimension];
    
    // 标准化排除的维度
    excludedDimensions = excludedDimensions.map(normalizeDimension);
    
    // 如果当前是商品相关维度，排除商品和商品名称
    if (actualCurrentDimension === '商品' || actualCurrentDimension === '商品名称') {
      excludedDimensions.push('商品', '商品名称');
    }
    
    // 如果是交叉维度，还需要排除交叉维度中已使用的维度
    if (isCrossDimension && currentDimension && currentDimension.includes('-')) {
      const crossDimensions = currentDimension.split('-');
      excludedDimensions = [...excludedDimensions, ...crossDimensions.map(normalizeDimension)];
    }
    
    // 获取可以继续下钻的维度：排除已使用的维度
    const availableDimensions = selectedAnalysisDimensions.filter(dim => {
      const normalizedDim = normalizeDimension(dim);
      return !excludedDimensions.includes(normalizedDim) && !excludedDimensions.includes(dim);
    });
    
    // 检查是否启用了指标拆解功能
    const isMetricsDecompositionEnabled = () => {
      // 如果没有归因设置，则不启用
      if (!attributionSettings) return false;
      
      // 严格检查：只有当用户明确勾选了指标归因时才启用
      // 这确保了只有在归因设置中选择了指标归因，才会显示"查看指标拆解"选项
      return attributionSettings.enableMetricAttribution === true;
    };
    
    const items = [];
    
    // 只有在启用指标拆解功能时才添加查看指标拆解选项
    if (isMetricsDecompositionEnabled()) {
      items.push({
        key: 'view-metrics',
        icon: <EyeOutlined />,
        label: '查看指标拆解',
        onClick: () => {
          setSelectedMetricsValue(clickedValue);
          setSelectedMetricsDimension(actualCurrentDimension);
          setMetricsModalVisible(true);
        }
      });
    }

    // 添加下钻选项
    availableDimensions.forEach(dim => {
      const dimensionNameMap = {
        '平台': '平台',
        '省份': '省份',
        '城市': '城市',
        '零售商': '零售商',
        '子品牌': '子品牌',
        '商品名称': '商品',
        '券机制': '券机制',
        '券门槛': '券门槛',
        '优惠力度': '优惠力度'
      };
      
      items.push({
        key: `drill-${dim}`,
        icon: <ArrowRightOutlined />,
        label: `下钻至${dimensionNameMap[dim] || dim}`,
        onClick: () => {
          if (onDrillDown) {
            // 对于交叉维度，需要特殊处理currentDimension参数
            let drillCurrentDimension = actualCurrentDimension;
            
            // 如果是交叉维度且没有下钻路径，使用交叉维度组合名称
            if (isCrossDimension && (!drillDownPath || drillDownPath.length === 0)) {
              drillCurrentDimension = currentDimension;
            }
            

            
            // 传递完整的行数据和正确的当前维度
            onDrillDown(clickedValue, drillCurrentDimension, dim, record);
          }
        }
      });
    });

    return items;
  }, [selectedAnalysisDimensions, drillDownPath, currentDimension, isCrossDimension, attributionSettings, setSelectedMetricsValue, setSelectedMetricsDimension, setMetricsModalVisible, onDrillDown, drillDownData]);
  
  // 确保 data 是数组
  if (!Array.isArray(data)) {
    return <Text type="secondary" style={{ textAlign: 'center', display: 'block', padding: 20 }}>数据格式错误</Text>;
  }

  if (data.length === 0) {
    return <Text type="secondary" style={{ textAlign: 'center', display: 'block', padding: 20 }}>数据为空</Text>;
  }

  // 构建显示数据：如果有下钻路径，只显示下钻数据；否则显示原始数据
  const getDisplayData = () => {
    // 如果有下钻路径，说明用户已经下钻，只显示下钻数据
    if (drillDownPath && drillDownPath.length > 0) {
      // 查找对应的下钻数据
      if (drillDownData && typeof drillDownData === 'object') {
        const drillDownKey = Object.keys(drillDownData).find(key => {
          // 构建期望的缓存键格式（匹配generateNestedCacheKey格式）
          const pathString = drillDownPath.map(p => `${p.dimension}:${p.value}`).join('|');
          return key.includes(pathString);
        });

        if (drillDownKey && drillDownData[drillDownKey]) {
          return drillDownData[drillDownKey];
        }
      }
    }
    
    // 没有下钻或没有找到下钻数据，返回原始数据
    return data;
  };

  const displayData = getDisplayData();

  // 判断是否为活动GMV数据
  const isActivityGMV = displayData.length > 0 && 'ROI当前值' in displayData[0];



  // 根据维度确定列的顺序和显示名称
  const getColumns = (dimension, isActivityGMV) => {
    // 如果是交叉维度，直接使用数据的所有字段作为列
    if (isCrossDimension && displayData && displayData.length > 0) {
      const allColumns = Object.keys(displayData[0]);
      
      // 查找维度字段（应该在第一列）
      const dimensionFields = ['省份', '城市', '平台', '零售商', '子品牌', '商品', '商品名称', '券机制', '券门槛', '优惠力度'];
      let dimensionColumn = null;
      
      for (const field of dimensionFields) {
        if (allColumns.includes(field)) {
          dimensionColumn = field;
          break;
        }
      }
      
      if (dimensionColumn) {
        // 按照用户要求的顺序：维度值、当期GMV、当期GMV占比、对比期GMV、对比期GMV占比、GMV变化值、GMV变化率、GMV贡献度
        const orderedColumns = [dimensionColumn];
        
        // 如果是商品维度，在维度列后面添加UPC列
        if ((dimensionColumn === '商品' || dimensionColumn === '商品名称') && allColumns.includes('UPC')) {
          orderedColumns.push('UPC');
        }
        
        // 根据数据类型确定字段顺序
        const isActivityData = allColumns.some(col => col.includes('活动GMV'));
        
        if (isActivityData) {
          // 活动数据顺序
          const preferredOrder = [
            "当期活动GMV",
            "当期活动GMV占比",
            "对比期活动GMV",
            "对比期活动GMV占比",
            "活动GMV变化值",
            "活动GMV变化率",
            "活动GMV贡献度",
            "消耗当前值",
            "消耗变化值",
            "消耗变化率",
            "消耗贡献度",
            "ROI当前值",
            "ROI变化值",
            "ROI变化率"
          ];
          
          preferredOrder.forEach(field => {
            if (allColumns.includes(field)) {
              orderedColumns.push(field);
            }
          });
        } else {
          // GMV数据顺序：维度值、当期GMV、当期GMV占比、对比期GMV、对比期GMV占比、GMV变化值、GMV变化率、GMV贡献度
          const preferredOrder = [
            "当期GMV",
            "当期GMV占比",
            "对比期GMV",
            "对比期GMV占比",
            "GMV变化值",
            "GMV变化率",
            "GMV贡献度"
          ];
          
          preferredOrder.forEach(field => {
            if (allColumns.includes(field)) {
              orderedColumns.push(field);
            }
          });
        }
        
        // 添加任何其他未包含的字段
        allColumns.forEach(field => {
          if (!orderedColumns.includes(field)) {
            orderedColumns.push(field);
          }
        });
        
        return {
          columns: orderedColumns,
          nameKey: dimensionColumn
        };
      }
      
      // 如果没有找到维度字段，使用原始顺序
      return {
        columns: allColumns,
        nameKey: allColumns[0]
      };
    }

    // 确定当前显示的维度：如果有下钻路径，使用最后一个下钻的目标维度；否则使用原始维度
    let currentDimensionForColumns = dimension;

    const baseColumns = {
      商品: ["商品", "UPC"],
      子品牌: ["子品牌"],
      省份: ["省份"],
      城市: ["城市"],
      平台: ["平台"],
      零售商: ["零售商"],
      券机制: ["券机制"],
      券门槛: ["券门槛"],
      优惠力度: ["优惠力度"]
    };

    const gmvColumns = isActivityGMV ? [
      "当期活动GMV",
      "当期活动GMV占比",
      "对比期活动GMV",
      "对比期活动GMV占比",
      "活动GMV变化值",
      "活动GMV变化率",
      "活动GMV贡献度",
      "消耗当前值",
      "消耗变化值",
      "消耗变化率",
      "消耗贡献度",
      "ROI当前值",
      "ROI变化值",
      "ROI变化率"
    ] : [
      "当期GMV",
      "当期GMV占比",
      "对比期GMV", 
      "对比期GMV占比",
      "GMV变化值",
      "GMV变化率",
      "GMV贡献度"
    ];

    // 首先尝试使用当前维度（可能是下钻后的维度）
    let config = baseColumns[currentDimensionForColumns];
    
    // 如果没有找到配置，尝试使用原始维度
    if (!config) {
      config = baseColumns[dimension];
    }
    
    // 如果还是没有找到，使用数据的第一个字段
    if (!config && displayData.length > 0) {
      return { columns: Object.keys(displayData[0]), nameKey: Object.keys(displayData[0])[0] };
    }

    // 如果有下钻数据，按照用户要求的字段顺序返回
    if (drillDownPath && drillDownPath.length > 0 && displayData && displayData.length > 0) {
      // 从下钻数据中获取所有可用字段
      const allColumns = Object.keys(displayData[0]);
      
      // 查找维度字段（应该在第一列）
      const dimensionFields = ['省份', '城市', '平台', '零售商', '子品牌', '商品', '商品名称', '券机制', '券门槛', '优惠力度'];
      let dimensionColumn = null;
      
      for (const field of dimensionFields) {
        if (allColumns.includes(field)) {
          dimensionColumn = field;
          break;
        }
      }
      
      if (dimensionColumn) {
        // 按照用户要求的顺序：维度值、当期GMV、当期GMV占比、对比期GMV、对比期GMV占比、GMV变化值、GMV变化率、GMV贡献度
        const orderedColumns = [dimensionColumn];
        
        // 如果是商品维度，在维度列后面添加UPC列
        if ((dimensionColumn === '商品' || dimensionColumn === '商品名称') && allColumns.includes('UPC')) {
          orderedColumns.push('UPC');
        }
        
        // 根据数据类型确定字段顺序
        const isActivityData = allColumns.some(col => col.includes('活动GMV'));
        
        if (isActivityData) {
          // 活动数据顺序
          const preferredOrder = [
            "当期活动GMV",
            "当期活动GMV占比",
            "对比期活动GMV",
            "对比期活动GMV占比",
            "活动GMV变化值",
            "活动GMV变化率",
            "活动GMV贡献度",
            "消耗当前值",
            "消耗变化值",
            "消耗变化率",
            "消耗贡献度",
            "ROI当前值",
            "ROI变化值",
            "ROI变化率"
          ];
          
          preferredOrder.forEach(field => {
            if (allColumns.includes(field)) {
              orderedColumns.push(field);
            }
          });
        } else {
          // GMV数据顺序：维度值、当期GMV、当期GMV占比、对比期GMV、对比期GMV占比、GMV变化值、GMV变化率、GMV贡献度
          const preferredOrder = [
            "当期GMV",
            "当期GMV占比",
            "对比期GMV",
            "对比期GMV占比",
            "GMV变化值",
            "GMV变化率",
            "GMV贡献度"
          ];
          
          preferredOrder.forEach(field => {
            if (allColumns.includes(field)) {
              orderedColumns.push(field);
            }
          });
        }
        
        // 添加任何其他未包含的字段
        allColumns.forEach(field => {
          if (!orderedColumns.includes(field)) {
            orderedColumns.push(field);
          }
        });
        
        return {
          columns: orderedColumns,
          nameKey: dimensionColumn
        };
      }
      
      // 如果没有找到维度字段，使用原始顺序
      return {
        columns: allColumns,
        nameKey: allColumns[0]
      };
    }

    return {
      columns: [...config, ...gmvColumns],
      nameKey: config[0]
    };
  };

  const { columns } = getColumns(dimension, isActivityGMV);

  // 格式化数值
  const formatValue = (value, column) => {
    if (value === null || value === undefined || value === '-') return '-';

    // 处理百分比
    if (column.includes('变化率') || column.includes('贡献度') || column.includes('占比')) {
      return value;
    }

    // 处理ROI
    if (column.includes('ROI')) {
      return value === '-' ? value : Number(value).toFixed(2);
    }

    // 为对比期GMV、当期GMV、GMV变化值、当期活动GMV、对比期活动GMV、活动GMV变化值添加千分位符，不保留小数点
    if (column === '对比期GMV' || column === '当期GMV' || column === 'GMV变化值' || 
        column === '当期活动GMV' || column === '对比期活动GMV' || column === '活动GMV变化值' ||
        column === '消耗当前值' || column === '消耗变化值') {
      const num = Number(value);
      if (isNaN(num)) return value;
      return Math.round(num).toLocaleString('zh-CN');
    }

    // 处理其他金额
    if (column.includes('GMV') || column.includes('消耗')) {
      return value === '-' ? value : Number(value).toString();
    }

    return value;
  };

  // 获取行的颜色 - 基于变化率，绿涨红跌，0为黑色
  const getRowColor = (row) => {
    // 选择正确的变化率字段
    let changeRate;
    if (isActivityGMV && '活动GMV变化率' in row) {
      changeRate = row['活动GMV变化率'];
    } else if ('GMV变化率' in row) {
      changeRate = row['GMV变化率'];
    } else {
      // 尝试找到任何变化率字段
      const changeRateKey = Object.keys(row).find(key => key.includes('变化率'));
      changeRate = changeRateKey ? row[changeRateKey] : null;
    }

    if (changeRate && changeRate !== '-') {
      const numValue = parseFloat(changeRate.toString().replace(/[%,+]/g, ''));
      if (!isNaN(numValue)) {
        if (numValue === 0) return { color: '#333' }; // 变化率为0时显示黑色
        // 绿涨红跌
        return {
          color: numValue > 0 ? '#52c41a' : '#f5222d'
        };
      }
    }
    return { color: '#333' }; // 默认颜色
  };

  // 动态计算列宽度，确保表头文字有足够空间
  const calculateColumnWidth = (columnTitle, index) => {
    // 基础宽度：每个中文字符大约16px，英文字符12px + padding + margin
    const chineseCharWidth = 16;

    const padding = 24; // 左右padding各12px
    const minWidth = 80;
    
    // 计算文字宽度（粗略估算中英文字符）
    const titleLength = columnTitle.length;
    // 假设大部分是中文字符，使用中文字符宽度
    const calculatedWidth = titleLength * chineseCharWidth + padding + 20; // 额外增加20px缓冲
    
    // 根据列类型设置不同的最小宽度
    let typeMinWidth = minWidth;
    if (index === 0) {
      typeMinWidth = 180; // 第一列需要更多空间
    } else if (columnTitle.includes('当期') || columnTitle.includes('对比期') || columnTitle.includes('变化值')) {
      typeMinWidth = 140;
    } else if (columnTitle.includes('变化率') || columnTitle.includes('贡献度') || columnTitle.includes('占比')) {
      typeMinWidth = 130;
    } else if (columnTitle.includes('ROI') || columnTitle.includes('消耗')) {
      typeMinWidth = 120;
    } else {
      typeMinWidth = 110;
    }
    
    // 返回计算出的宽度和类型最小宽度中的较大值
    return Math.max(calculatedWidth, typeMinWidth);
  };

  // 构建 antd Table 需要的列定义
  const tableColumns = columns.map((column, index) => {
    // 根据列内容类型设置不同的宽度配置
    let columnConfig = {
      title: column === '商品名称' ? 'UPC' : column,
      dataIndex: column,
      key: column,
      // 移除ellipsis设置，确保表头文字不被省略
      onHeaderCell: () => ({
        style: {
          whiteSpace: 'nowrap', // 表头不换行
          padding: '8px 12px',
          fontSize: '14px', // 调小表头字体大小
          fontWeight: 'bold', // 改为bold，确保表头字段名加粗
          color: '#262626', // 加重文字颜色
          background: '#fafafa',
          borderBottom: '1px solid #f0f0f0',
          textAlign: 'left', // 所有表头字段都靠左对齐
          // 移除溢出隐藏，确保表头文字完整显示
          overflow: 'visible',
          textOverflow: 'unset'
        }
      }),
    };

    // 根据列类型设置宽度 - 使用动态计算
    const calculatedWidth = calculateColumnWidth(column, index);
    columnConfig.width = calculatedWidth;
    columnConfig.minWidth = calculatedWidth;

    // 添加排序功能
    columnConfig.sorter = (a, b) => {
      const aValue = a[column]?.toString().replace(/[%,元]/g, '') || '';
      const bValue = b[column]?.toString().replace(/[%,元]/g, '') || '';

      const aNum = parseFloat(aValue);
      const bNum = parseFloat(bValue);

      if (isNaN(aNum) || isNaN(bNum)) {
        return aValue.localeCompare(bValue, 'zh-CN');
      }

      return aNum - bNum;
    };

    // 为当期GMV列设置默认降序排序
    if (column === '当期GMV' || column === '当期活动GMV') {
      columnConfig.defaultSortOrder = 'descend';
    }

    // 添加render函数
    columnConfig.render = (text, record) => {
      const value = formatValue(text, column);



      // 为第一列添加下钻功能和缩进处理
      if (index === 0) {
        // 计算缩进级别 - 下钻数据需要缩进
        const indentLevel = record._isDrillDown ? (record._level || 1) : 0;
        const indentStyle = {
          paddingLeft: `${indentLevel * 20}px`, // 每级缩进20px
          position: 'relative'
        };

        // 为下钻数据添加连接线样式
        const drillDownStyle = record._isDrillDown ? {
          ...indentStyle,
          '&::before': {
            content: '""',
            position: 'absolute',
            left: `${(indentLevel - 1) * 20 + 10}px`,
            top: '0',
            bottom: '0',
            width: '1px',
            backgroundColor: '#d9d9d9'
          }
        } : indentStyle;

        // 检查是否可以下钻
        if (selectedAnalysisDimensions && selectedAnalysisDimensions.length > 0 && !record._isDrillDown) {
          const drillOptions = getDrillDownOptions(text, record);

          if (drillOptions.length > 0) {
            return (
              <Dropdown
                menu={{ items: drillOptions }}
                trigger={['click']}
                placement="bottomLeft"
                getPopupContainer={() => document.body}
                overlayStyle={{
                  zIndex: 10000,
                  position: 'fixed'
                }}
                destroyPopupOnHide={false}
              >
                <span
                  style={{
                    ...drillDownStyle,
                    color: '#1890ff',
                    cursor: 'pointer',
                    textDecoration: 'underline',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: 'block'
                  }}
                  onClick={(e) => e.preventDefault()}
                  title={value}
                >
                  {value}
                </span>
              </Dropdown>
            );
          }
        }

        // 没有下钻选项或是下钻数据，直接显示文本
        return (
          <div
            style={{
              ...drillDownStyle,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              color: record._isDrillDown ? '#666' : '#333', // 指标名称列保持黑色，下钻数据使用较浅的颜色
              fontSize: record._isDrillDown ? '13px' : '14px' // 下钻数据使用较小的字体
            }}
            title={value}
          >
            {record._isDrillDown && '└ '}{value}
          </div>
        );
      }
      
      // 为贡献度字段添加粗体，但不单独设置颜色
      if (column.includes('贡献度')) {
        return (
          <span
            style={{
              fontWeight: 'bold',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: 'block',
              color: record._isDrillDown ? '#666' : 'inherit', // 下钻数据使用较浅的颜色
              fontSize: record._isDrillDown ? '13px' : '14px' // 下钻数据使用较小的字体
            }}
            title={value}
          >
            {value}
          </span>
        );
      }

      // 其他列的通用样式
      return (
        <span
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: 'block',
            color: record._isDrillDown ? '#666' : 'inherit', // 下钻数据使用较浅的颜色
            fontSize: record._isDrillDown ? '13px' : '14px' // 下钻数据使用较小的字体
          }}
          title={value}
        >
          {value}
        </span>
      );
    };

    return columnConfig;
  });

  // 处理数据，添加 key 属性
  const tableData = displayData.map((item, index) => {
    // 对于商品维度，将"商品"字段映射到"商品名称"
    const newItem = {...item, key: index};
    if (dimension === '商品' && item['商品'] && !item['商品名称']) {
      newItem['商品名称'] = item['商品'];
    }
    return newItem;
  });

  // 处理分页改变事件
  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  return (
    <div>
      {/* 面包屑导航 */}
      {drillDownPath && drillDownPath.length > 0 && (
        <div style={{ 
          marginBottom: '16px', 
          padding: '12px 16px', 
          background: '#f8f9fa', 
          borderRadius: '6px',
          border: '1px solid #e9ecef',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Breadcrumb
            separator=">"
            items={[
              // 根级别
              ...(selectedAnalysisDimensions && selectedAnalysisDimensions.length > 0 ? [{
                title: selectedAnalysisDimensions[0],
                href: '#',
                onClick: (e) => {
                  e.preventDefault();
                  onBreadcrumbClick && onBreadcrumbClick(-1);
                }
              }] : []),
              // 下钻路径项
              ...drillDownPath.map((item, index) => ({
                title: `${item.dimension}: ${item.value}`,
                href: index === drillDownPath.length - 1 ? undefined : '#',
                onClick: index === drillDownPath.length - 1 ? undefined : (e) => {
                  e.preventDefault();
                  onBreadcrumbClick && onBreadcrumbClick(index);
                }
              }))
            ]}
          />
          
          <div style={{ display: 'flex', gap: '8px' }}>
            {onGoBack && (
              <Button
                size="small"
                icon={<LeftOutlined />}
                onClick={onGoBack}
                style={{ 
                  borderColor: '#d9d9d9',
                  color: '#666'
                }}
              >
                返回上级
              </Button>
            )}
            {onReset && (
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={onReset}
                style={{ 
                  borderColor: '#d9d9d9',
                  color: '#666'
                }}
              >
                重置
              </Button>
            )}
          </div>
        </div>
      )}

      <div
        className="table-content-container"
        style={{
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
          overflowX: 'auto', // 启用横向滚动
          overflowY: 'hidden',
          border: '1px solid #f0f0f0',
          width: '100%'
        }}
      >
        <Table
          columns={tableColumns}
          dataSource={tableData}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: (total, range) => `${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handlePageChange,
            onShowSizeChange: handlePageChange,
            size: 'small',
            showQuickJumper: true
          }}
          size="middle"
          // 禁用Table自身的滚动，让外层容器处理
          scroll={false}
          // 添加表格样式
          style={{
            minWidth: '1000px' // 设置最小宽度确保表格不会过度压缩
          }}
          // 始终显示表头，确保下钻后表格有完整的列标题
          showHeader={true}
          onRow={(record, index) => {
            // 检查是否为下钻数据的第一行
            const isFirstDrillDownRow = record._isDrillDown &&
              (index === 0 || !tableData[index - 1]._isDrillDown);

            return {
              style: {
                // 恢复基于变化率的行颜色
                ...getRowColor(record),
                // 为下钻数据添加背景色和边框样式
                ...(record._isDrillDown ? {
                  backgroundColor: '#fafafa', // 下钻数据使用浅灰色背景
                  borderLeft: '3px solid #1890ff', // 左侧蓝色边框标识下钻数据
                  borderTop: isFirstDrillDownRow ? '1px solid #e8e8e8' : 'none', // 第一行下钻数据添加上边框
                } : {}),
                // 确保原始数据行与下钻数据行之间有清晰的分隔
                ...(index > 0 && !record._isDrillDown && tableData[index - 1]._isDrillDown ? {
                  borderTop: '2px solid #d9d9d9' // 下钻数据后的原始数据行添加较粗的上边框
                } : {})
              },
            };
          }}
          components={{
            header: {
              cell: (props) => (
                <th
                  {...props}
                  style={{
                    ...props.style,
                    whiteSpace: 'nowrap', // 表头不换行
                    padding: '8px 12px',
                    fontSize: '14px', // 调小表头字体大小
                    fontWeight: 'bold', // 改为bold，确保表头字段名加粗
                    color: '#262626', // 加重文字颜色
                    background: '#fafafa',
                    borderBottom: '1px solid #f0f0f0',
                    // 确保表头文字完整显示
                    overflow: 'visible',
                    textOverflow: 'unset'
                  }}
                />
              )
            },
            body: {
              cell: (props) => (
                <td
                  {...props}
                  style={{
                    ...props.style,
                    padding: '8px 10px', // 稍微增加padding
                    fontSize: '14px', // 调整表格内容字体大小为14px
                    borderBottom: '1px solid #f5f5f5'
                  }}
                />
              )
            }
          }}
        />
      </div>

      {/* 指标拆解弹窗 */}
      <MetricsDecompositionModal
        visible={metricsModalVisible}
        onClose={() => {
          setMetricsModalVisible(false);
          setSelectedMetricsValue('');
          setSelectedMetricsDimension('');
        }}
        selectedValue={selectedMetricsValue}
        selectedDimension={selectedMetricsDimension}
        data={data}
        drillDownPath={drillDownPath}
        drillDownData={drillDownData}
        isActivityGMV={isActivityGMV}
        dateParams={dateParams}
        attributionSettings={attributionSettings}
        selectedAnalysisDimensions={selectedAnalysisDimensions}
        filterConditions={filterConditions}
      />
    </div>
  );
});

const ResultTable = React.memo(({
  sheets,
  activeTab,
  onTabChange,
  hideTabNavigation = false,
  isCrossDimension = false,
  selectedAnalysisDimensions = [], // 传递选中的分析维度
  onDrillDown = null, // 下钻回调
  drillDownPath = [], // 下钻路径
  onGoBack = null, // 返回上级回调
  onReset = null, // 重置回调
  onBreadcrumbClick = null, // 面包屑点击回调
  expandedRows = new Set(), // 展开的行
  drillDownData = {}, // 下钻数据
  attributionSettings = null, // 归因设置信息
  // 新增日期参数
  dateParams = null,
  // 新增筛选条件参数
  filterConditions = null
}) => {


  // 维度映射
  const dimensionMap = {
    'overall': '整体',
    'platform': '平台',
    'region': '省份',
    'city': '城市',
    'retailer': '零售商',
    'sub_brand': '子品牌',
    'product': '商品',
    'coupon_mechanism': '券机制',
    'coupon_threshold': '券门槛',
    'discount_rate': '优惠力度',
    // 添加中文维度映射
    '平台': '平台',
    '省份': '省份',
    '城市': '城市',
    '零售商': '零售商',
    '子品牌': '子品牌',
    '商品名称': '商品',
    '券机制': '券机制',
    '券门槛': '券门槛',
    '优惠力度': '优惠力度'
  };





  // 检查是否有数据
  if (!sheets || Object.keys(sheets).length === 0) {
    return null;
  }

  // 如果hideTabNavigation为true，只显示指定维度的内容
  if (hideTabNavigation && activeTab) {
    const dimension = dimensionMap[activeTab] || activeTab;
    let data;
    
    if (isCrossDimension) {
      // 对于交叉维度，数据结构可能不同
      data = sheets[activeTab];
      
      // 如果是交叉维度且数据有特殊结构（如headers和rows）
      if (data && data.headers && data.rows) {
        // 将headers和rows转换为标准的对象数组格式
        const convertedData = data.rows.map(row => {
          const obj = {};
          data.headers.forEach((header, index) => {
            obj[header] = row[index];
          });
          return obj;
        });
        data = convertedData;
      }
    } else {
      data = sheets[dimension];
    }
    
    if (!data || data.length === 0) {
      return (
        <div style={{ 
          padding: '40px 20px', 
          textAlign: 'center', 
          color: '#999'
        }}>
          暂无{dimension}数据
        </div>
      );
    }

    return (
      <Card bordered={false}>
        <Card 
          style={{ marginBottom: '16px' }}
          headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
          bodyStyle={{ padding: '16px', background: '#fafafa' }}
        >
          <Top3Content data={isCrossDimension ? { [activeTab]: data } : sheets} dimension={dimension} />
        </Card>
        <Collapse
          items={[
            {
              key: `${dimension}-details`,
              label: '详细数据',
              children: <TableContent
                data={data}
                dimension={dimension}
                isCrossDimension={isCrossDimension}
                selectedAnalysisDimensions={selectedAnalysisDimensions}
                                    currentDimension={dimension}
                onDrillDown={onDrillDown}

                drillDownPath={drillDownPath}
                onGoBack={onGoBack}
                onReset={onReset}
                onBreadcrumbClick={onBreadcrumbClick}
                expandedRows={expandedRows}
                drillDownData={drillDownData}
                attributionSettings={attributionSettings}
                // 新增日期参数
                dateParams={dateParams}
                filterConditions={filterConditions}
              />
            }
          ]}
        />
      </Card>
    );
  }

  // 判断是否为活动GMV数据
  const hasActivityGMV = sheets['优惠力度'] || sheets['券机制'] || sheets['券门槛'];

  // 创建标签页项 - 整体始终是第一个
  const items = [
    {
      key: 'overall',
      label: '整体',
      children: (
        <Card bordered={false}>
          <Card 
            style={{ marginBottom: '16px' }}
            headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
            bodyStyle={{ padding: '16px', background: '#fafafa' }}
          >
            <Top3Content data={sheets} dimension="整体" />
          </Card>
          {sheets['整体'] && sheets['整体'].length > 0 && (
            <Collapse
              items={[
                {
                  key: 'overall-details',
                  label: '详细数据',
                  children: <TableContent
                    data={sheets['整体']}
                    dimension="整体"
                    selectedAnalysisDimensions={selectedAnalysisDimensions}
                    currentDimension="整体"
                    onDrillDown={onDrillDown}

                    drillDownPath={drillDownPath}
                    onGoBack={onGoBack}
                    onReset={onReset}
                    onBreadcrumbClick={onBreadcrumbClick}
                    expandedRows={expandedRows}
                    drillDownData={drillDownData}
                    attributionSettings={attributionSettings}
                    // 新增日期参数
                    dateParams={dateParams}
                    filterConditions={filterConditions}
                  />
                }
              ]}
            />
          )}
        </Card>
      )
    }
  ];
  
  // 按照新的顺序添加平台维度
  if (sheets['平台'] && sheets['平台'].length > 0) {
    items.push({
      key: 'platform',
      label: '平台',
      children: (
        <Card bordered={false}>
          <Card 
            style={{ marginBottom: '16px' }}
            headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
            bodyStyle={{ padding: '16px', background: '#fafafa' }}
          >
            <Top3Content data={sheets} dimension="平台" />
          </Card>
          <Collapse
            items={[
              {
                key: 'platform-details',
                label: '详细数据',
                children: <TableContent
                  data={sheets['平台']}
                  dimension="平台"
                  selectedAnalysisDimensions={selectedAnalysisDimensions}
                  currentDimension="平台"
                  onDrillDown={onDrillDown}

                  drillDownPath={drillDownPath}
                  onGoBack={onGoBack}
                  onReset={onReset}
                  onBreadcrumbClick={onBreadcrumbClick}
                  expandedRows={expandedRows}
                  drillDownData={drillDownData}
                  attributionSettings={attributionSettings}
                  // 新增日期参数
                  dateParams={dateParams}
                  filterConditions={filterConditions}
                />
              }
            ]}
          />
        </Card>
      )
    });
  }
  
  // 省份维度
  if (sheets['省份'] && sheets['省份'].length > 0) {
    items.push({
      key: 'region',
      label: '省份',
      children: (
        <Card bordered={false}>
          <Card 
            style={{ marginBottom: '16px' }}
            headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
            bodyStyle={{ padding: '16px', background: '#fafafa' }}
          >
            <Top3Content data={sheets} dimension="省份" />
          </Card>
          <Collapse
            items={[
              {
                key: 'region-details',
                label: '详细数据',
                children: <TableContent 
                  data={sheets['省份']} 
                  dimension="省份"
                  selectedAnalysisDimensions={selectedAnalysisDimensions}
                  currentDimension="省份"
                  onDrillDown={onDrillDown}

                  drillDownPath={drillDownPath}
                  onGoBack={onGoBack}
                  onReset={onReset}
                  onBreadcrumbClick={onBreadcrumbClick}
                  expandedRows={expandedRows}
                  drillDownData={drillDownData}
                  attributionSettings={attributionSettings}
                  // 新增日期参数
                  dateParams={dateParams}
                  filterConditions={filterConditions}
                />
              }
            ]}
          />
        </Card>
      )
    });
  }
  
  // 城市维度
  if (sheets['城市'] && sheets['城市'].length > 0) {
    items.push({
      key: 'city',
      label: '城市',
      children: (
        <Card bordered={false}>
          <Card 
            style={{ marginBottom: '16px' }}
            headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
            bodyStyle={{ padding: '16px', background: '#fafafa' }}
          >
            <Top3Content data={sheets} dimension="城市" />
          </Card>
          <Collapse
            items={[
              {
                key: 'city-details',
                label: '详细数据',
                children: <TableContent 
                  data={sheets['城市']} 
                  dimension="城市"
                  selectedAnalysisDimensions={selectedAnalysisDimensions}
                  currentDimension="城市"
                  onDrillDown={onDrillDown}

                  drillDownPath={drillDownPath}
                  onGoBack={onGoBack}
                  onReset={onReset}
                  onBreadcrumbClick={onBreadcrumbClick}
                  expandedRows={expandedRows}
                  drillDownData={drillDownData}
                  attributionSettings={attributionSettings}
                  // 新增日期参数
                  dateParams={dateParams}
                  filterConditions={filterConditions}
                />
              }
            ]}
          />
        </Card>
      )
    });
  }
  
  // 零售商维度
  if (sheets['零售商'] && sheets['零售商'].length > 0) {
    items.push({
      key: 'retailer',
      label: '零售商',
      children: (
        <Card bordered={false}>
          <Card 
            style={{ marginBottom: '16px' }}
            headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
            bodyStyle={{ padding: '16px', background: '#fafafa' }}
          >
            <Top3Content data={sheets} dimension="零售商" />
          </Card>
          <Collapse
            items={[
              {
                key: 'retailer-details',
                label: '详细数据',
                children: <TableContent 
                  data={sheets['零售商']} 
                  dimension="零售商"
                  selectedAnalysisDimensions={selectedAnalysisDimensions}
                  currentDimension="零售商"
                  onDrillDown={onDrillDown}

                  drillDownPath={drillDownPath}
                  onGoBack={onGoBack}
                  onReset={onReset}
                  onBreadcrumbClick={onBreadcrumbClick}
                  expandedRows={expandedRows}
                  drillDownData={drillDownData}
                  attributionSettings={attributionSettings}
                  // 新增日期参数
                  dateParams={dateParams}
                  filterConditions={filterConditions}
                />
              }
            ]}
          />
        </Card>
      )
    });
  }
  
  // 子品牌维度
  if (sheets['子品牌'] && sheets['子品牌'].length > 0) {
    items.push({
      key: 'sub_brand',
      label: '子品牌',
      children: (
        <Card bordered={false}>
          <Card 
            style={{ marginBottom: '16px' }}
            headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
            bodyStyle={{ padding: '16px', background: '#fafafa' }}
          >
            <Top3Content data={sheets} dimension="子品牌" />
          </Card>
          <Collapse
            items={[
              {
                key: 'sub_brand-details',
                label: '详细数据',
                children: <TableContent 
                  data={sheets['子品牌']} 
                  dimension="子品牌"
                  selectedAnalysisDimensions={selectedAnalysisDimensions}
                  currentDimension="子品牌"
                  onDrillDown={onDrillDown}

                  drillDownPath={drillDownPath}
                  onGoBack={onGoBack}
                  onReset={onReset}
                  onBreadcrumbClick={onBreadcrumbClick}
                  expandedRows={expandedRows}
                  drillDownData={drillDownData}
                  attributionSettings={attributionSettings}
                  // 新增日期参数
                  dateParams={dateParams}
                  filterConditions={filterConditions}
                />
              }
            ]}
          />
        </Card>
      )
    });
  }
  
  // 根据GMV类型添加不同的维度
  if (hasActivityGMV) {
    // 活动GMV顺序: 整体、平台、省份、城市、零售商、子品牌、券机制、券门槛、优惠力度
    
    // 添加券机制维度
    if (sheets['券机制'] && sheets['券机制'].length > 0) {
      items.push({
        key: 'coupon_mechanism',
        label: '券机制',
        children: (
          <Card bordered={false}>
            <Card 
              style={{ marginBottom: '16px' }}
              headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
              bodyStyle={{ padding: '16px', background: '#fafafa' }}
            >
              <Top3Content data={sheets} dimension="券机制" />
            </Card>
            <Collapse
              items={[
                {
                  key: 'coupon_mechanism-details',
                  label: '详细数据',
                  children: <TableContent 
                    data={sheets['券机制']} 
                    dimension="券机制"
                    selectedAnalysisDimensions={selectedAnalysisDimensions}
                    currentDimension="券机制"
                    onDrillDown={onDrillDown}

                    drillDownPath={drillDownPath}
                    onGoBack={onGoBack}
                    onReset={onReset}
                    onBreadcrumbClick={onBreadcrumbClick}
                    expandedRows={expandedRows}
                    drillDownData={drillDownData}
                    attributionSettings={attributionSettings}
                    // 新增日期参数
                    dateParams={dateParams}
                    filterConditions={filterConditions}
                  />
                }
              ]}
            />
          </Card>
        )
      });
    }

    // 添加券门槛维度
    if (sheets['券门槛'] && sheets['券门槛'].length > 0) {
      items.push({
        key: 'coupon_threshold',
        label: '券门槛',
        children: (
          <Card bordered={false}>
            <Card 
              style={{ marginBottom: '16px' }}
              headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
              bodyStyle={{ padding: '16px', background: '#fafafa' }}
            >
              <Top3Content data={sheets} dimension="券门槛" />
            </Card>
            <Collapse
              items={[
                {
                  key: 'coupon_threshold-details',
                  label: '详细数据',
                  children: <TableContent 
                    data={sheets['券门槛']} 
                    dimension="券门槛"
                    selectedAnalysisDimensions={selectedAnalysisDimensions}
                    currentDimension="券门槛"
                    onDrillDown={onDrillDown}

                    drillDownPath={drillDownPath}
                    onGoBack={onGoBack}
                    onReset={onReset}
                    onBreadcrumbClick={onBreadcrumbClick}
                    expandedRows={expandedRows}
                    drillDownData={drillDownData}
                    attributionSettings={attributionSettings}
                    // 新增日期参数
                    dateParams={dateParams}
                    filterConditions={filterConditions}
                  />
                }
              ]}
            />
          </Card>
        )
      });
    }

    // 添加优惠力度维度
    if (sheets['优惠力度'] && sheets['优惠力度'].length > 0) {
      items.push({
        key: 'discount_rate',
        label: '优惠力度',
        children: (
          <Card bordered={false}>
            <Card 
              style={{ marginBottom: '16px' }}
              headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
              bodyStyle={{ padding: '16px', background: '#fafafa' }}
            >
              <Top3Content data={sheets} dimension="优惠力度" />
            </Card>
            <Collapse
              items={[
                {
                  key: 'discount_rate-details',
                  label: '详细数据',
                  children: <TableContent 
                    data={sheets['优惠力度']} 
                    dimension="优惠力度"
                    selectedAnalysisDimensions={selectedAnalysisDimensions}
                    currentDimension="优惠力度"
                    onDrillDown={onDrillDown}

                    drillDownPath={drillDownPath}
                    onGoBack={onGoBack}
                    onReset={onReset}
                    onBreadcrumbClick={onBreadcrumbClick}
                    expandedRows={expandedRows}
                    drillDownData={drillDownData}
                    attributionSettings={attributionSettings}
                    // 新增日期参数
                    dateParams={dateParams}
                    filterConditions={filterConditions}
                  />
                }
              ]}
            />
          </Card>
        )
      });
    }
  } else {
    // 全量GMV顺序: 整体、平台、省份、城市、零售商、子品牌、商品
    // 添加商品维度 (仅适用于全量GMV)
    if (sheets['商品'] && sheets['商品'].length > 0) {
      items.push({
        key: 'product',
        label: '商品',
        children: (
          <Card bordered={false}>
            <Card 
              style={{ marginBottom: '16px' }}
              headStyle={{ background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}
              bodyStyle={{ padding: '16px', background: '#fafafa' }}
            >
              <Top3Content data={sheets} dimension="商品" />
            </Card>
            <Collapse
              items={[
                {
                  key: 'product-details',
                  label: '详细数据',
                  children: <TableContent 
                  data={sheets['商品']} 
                  dimension="商品"
                  selectedAnalysisDimensions={selectedAnalysisDimensions}
                  currentDimension="商品"
                  onDrillDown={onDrillDown}

                  drillDownPath={drillDownPath}
                  onGoBack={onGoBack}
                  onReset={onReset}
                  onBreadcrumbClick={onBreadcrumbClick}
                  expandedRows={expandedRows}
                  drillDownData={drillDownData}
                  attributionSettings={attributionSettings}
                  // 新增日期参数
                  dateParams={dateParams}
                  filterConditions={filterConditions}
                />
                }
              ]}
            />
          </Card>
        )
      });
    }
  }

  return (
    <Tabs
      activeKey={activeTab}
      onChange={onTabChange}
      items={items}
    />
  );
});

export default ResultTable;
export { SupplySideSection as SupplySideTable, MarketingSideTable }; 